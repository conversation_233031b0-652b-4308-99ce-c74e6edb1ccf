import React from 'react';
import ApiTest from '@/components/ApiTest';
import DeleteIncidentTest from '@/components/DeleteIncidentTest';
import DynamicTitlesTest from '@/components/DynamicTitlesTest';
import AllIncidentsApiTest from '@/components/AllIncidentsApiTest';
import ReviewerActionsApiTest from '@/components/ReviewerActionsApiTest';
import ReporterIncidentsApiTest from '@/components/ReporterIncidentsApiTest';
import TokenDebugTest from '@/components/TokenDebugTest';
import TokenQuickTest from '@/components/TokenQuickTest';

const ApiTestPage = () => {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold mb-6 text-center">API Connection Test</h1>
      <TokenQuickTest />
      <TokenDebugTest />
      <ApiTest />
      <DynamicTitlesTest />
      <AllIncidentsApiTest />
      <ReviewerActionsApiTest />
      <ReporterIncidentsApiTest />
      <DeleteIncidentTest />
    </div>
  );
};

export default ApiTestPage;
