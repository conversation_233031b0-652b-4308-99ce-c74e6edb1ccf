import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService, getToken } from '@/lib/utils';
import { toast } from 'sonner';

const ReviewerActionsDebug = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDirectApiCall = async () => {
    setIsLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log('🔄 Testing direct API call to /actions/get/INCIDENT...');
      const token = getToken();
      
      if (!token) {
        throw new Error('No authentication token available');
      }

      const url = 'https://dev.stt-user.acuizen.com/actions/get/INCIDENT';
      console.log('🔗 Making request to:', url);
      console.log('🔑 Using token:', token.substring(0, 50) + '...');

      const fetchResponse = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log('📊 Response status:', fetchResponse.status);
      console.log('📊 Response headers:', Object.fromEntries(fetchResponse.headers.entries()));

      if (!fetchResponse.ok) {
        const errorText = await fetchResponse.text();
        console.error('❌ HTTP error:', fetchResponse.status, errorText);
        throw new Error(`HTTP ${fetchResponse.status}: ${errorText}`);
      }

      const data = await fetchResponse.json();
      console.log('✅ Direct API call successful:', data);
      setResponse(data);
      toast.success('Direct API call successful!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Direct API call failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Direct API call failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testApiServiceCall = async () => {
    setIsLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log('🔄 Testing ApiService.getReviewerActions()...');
      const data = await ApiService.getReviewerActions();
      console.log('✅ ApiService call successful:', data);
      setResponse(data);
      toast.success('ApiService call successful!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ ApiService call failed:', errorMessage);
      setError(errorMessage);
      toast.error(`ApiService call failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Reviewer Actions API Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4">
          <Button
            onClick={testDirectApiCall}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Test Direct API Call'}
          </Button>
          <Button
            onClick={testApiServiceCall}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Test ApiService Call'}
          </Button>
        </div>

        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/actions/get/INCIDENT
          </code>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h3 className="font-medium text-red-800 mb-2">Error:</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {response && (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <h3 className="font-medium text-green-800 mb-2">Response:</h3>
            <pre className="text-green-700 text-xs overflow-auto max-h-96">
              {JSON.stringify(response, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ReviewerActionsDebug;
