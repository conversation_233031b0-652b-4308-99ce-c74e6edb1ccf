import { useState, useEffect } from "react";
import { Search, Filter, X, AlertCircle, Clock, User, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Incident } from "@/contexts/IncidentContext";
import { format } from "date-fns";

interface MyActionsDataTableProps {
  data: Incident[];
  onTakeAction: (id: string) => void;
  onView: (incident: Incident) => void;
  isLoading?: boolean;
  dataSource?: 'api' | 'fallback';
}

const MyActionsDataTable: React.FC<MyActionsDataTableProps> = ({
  data,
  onTakeAction,
  onView,
  isLoading = false,
  dataSource = 'fallback'
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<Incident[]>(data);
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [priorityFilters, setPriorityFilters] = useState<string[]>([]);

  // Status options specific to reviewer actions
  const statusOptions = [
    { value: "reported", label: "Reported" },
    { value: "under-review", label: "Under Review" },
    { value: "submitted", label: "Preliminary Analysis" },
  ];

  // Priority options
  const priorityOptions = [
    { value: "high", label: "High Priority" },
    { value: "medium", label: "Medium Priority" },
    { value: "low", label: "Low Priority" },
  ];

  // Update filtered data when search term or filters change
  useEffect(() => {
    let result = [...data];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(incident =>
        incident.id?.toLowerCase().includes(term) ||
        incident.maskId?.toLowerCase().includes(term) ||
        incident.description?.toLowerCase().includes(term) ||
        incident.title?.toLowerCase().includes(term) ||
        incident.reportedBy?.toLowerCase().includes(term) ||
        incident.applicationDetails?.title?.toLowerCase().includes(term) ||
        incident.applicationDetails?.maskId?.toLowerCase().includes(term)
      );
    }

    // Apply status filters
    if (statusFilters.length > 0) {
      result = result.filter(incident => 
        statusFilters.includes(incident.status) ||
        statusFilters.includes(incident.applicationDetails?.status || '')
      );
    }

    // Apply priority filters (if priority field exists)
    if (priorityFilters.length > 0) {
      result = result.filter(incident => 
        priorityFilters.includes(incident.priority || 'medium')
      );
    }

    setFilteredData(result);
  }, [data, searchTerm, statusFilters, priorityFilters]);

  // Toggle status filter
  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Toggle priority filter
  const togglePriorityFilter = (priority: string) => {
    setPriorityFilters(prev =>
      prev.includes(priority)
        ? prev.filter(p => p !== priority)
        : [...prev, priority]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilters([]);
    setPriorityFilters([]);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case "reported":
        return "default";
      case "under-review":
        return "secondary";
      case "submitted":
        return "outline";
      default:
        return "default";
    }
  };

  // Get priority badge variant
  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  // Format incident date
  const formatIncidentDate = (incident: Incident) => {
    const dateToFormat = incident.applicationDetails?.incidentDate || incident.incidentDate;
    if (!dateToFormat) return 'N/A';
    
    try {
      return format(new Date(dateToFormat), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid Date';
    }
  };

  // Get incident title
  const getIncidentTitle = (incident: Incident) => {
    return incident.applicationDetails?.title || incident.title || incident.description || 'Untitled Incident';
  };

  // Get incident ID
  const getIncidentId = (incident: Incident) => {
    return incident.applicationDetails?.maskId || incident.maskId || incident.id;
  };

  // Get incident status
  const getIncidentStatus = (incident: Incident) => {
    return incident.applicationDetails?.status || incident.status;
  };

  // Get incident stage
  const getIncidentStage = (incident: Incident) => {
    return incident.applicationDetails?.stage || incident.stage || 'Initial Report';
  };

  const hasActiveFilters = statusFilters.length > 0 || priorityFilters.length > 0 || searchTerm.length > 0;

  return (
    <div className="space-y-4">
      {/* Header with data source indicator */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">My Actions</h2>
          <Badge variant={dataSource === 'api' ? 'default' : 'secondary'}>
            {dataSource === 'api' ? 'API Data' : 'Fallback Data'}
          </Badge>
          {data.length > 0 && (
            <Badge variant="outline">
              {data.length} incident{data.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search incidents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full sm:w-64"
            />
          </div>

          {/* Status Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Status
                {statusFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {statusFilters.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {statusOptions.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={statusFilters.includes(option.value)}
                  onCheckedChange={() => toggleStatusFilter(option.value)}
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="p-8 text-center border rounded-md">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <h3 className="text-lg font-medium mb-2">Loading your action items...</h3>
          <p className="text-muted-foreground">Fetching incidents that require your attention.</p>
        </div>
      )}

      {/* Data Table */}
      {!isLoading && (
        <div className="rounded-md border bg-card overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="bg-muted/50">
                <TableRow>
                  <TableHead className="font-semibold">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      Incident ID
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold">Title</TableHead>
                  <TableHead className="font-semibold">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Reported By
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Stage
                    </div>
                  </TableHead>
                  <TableHead className="text-right font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((incident) => (
                    <TableRow key={incident.id} className="hover:bg-muted/30 transition-colors">
                      <TableCell className="font-medium">
                        {getIncidentId(incident)}
                      </TableCell>
                      <TableCell>
                        {formatIncidentDate(incident)}
                      </TableCell>
                      <TableCell className="max-w-xs">
                        <div className="truncate" title={getIncidentTitle(incident)}>
                          {getIncidentTitle(incident)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {incident.reportedBy || 'Unknown'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(getIncidentStatus(incident))}>
                          {getIncidentStatus(incident)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {getIncidentStage(incident)}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex gap-2 justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onView(incident)}
                          >
                            View
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700"
                            onClick={() => onTakeAction(incident.id)}
                          >
                            Take Action
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-32 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <AlertCircle className="h-8 w-8 mb-2 opacity-40" />
                        <p className="font-medium">No incidents requiring action found.</p>
                        <p className="text-sm">
                          {hasActiveFilters 
                            ? "Try adjusting your filters or search criteria."
                            : "All incidents have been reviewed or no incidents are assigned to you."
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyActionsDataTable;
