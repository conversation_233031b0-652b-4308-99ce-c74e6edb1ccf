
import { z } from "zod";

export const incidentFormSchema = z.object({
  // Step 1 - Incident Information
  incidentTitle: z.string().min(1, "Incident title is required"),
  incidentDate: z.date(),
  incidentType: z.string().min(1, "Incident type is required"),
  description: z.string().min(1, "Description is required"),

  // Location information
  locationCountry: z.string().min(1, "Country is required"),
  locationCity: z.string().min(1, "City is required"),
  locationBusinessUnit: z.string().min(1, "Business unit is required"),
  locationProject: z.string().min(1, "Project/DC Ops is required"),
  locationDetails: z.string().min(1, "Level and location is required"),

  // Classification of Actual Injury
  isWorkRelated: z.boolean().nullable(),
  lossOfConsciousness: z.boolean().nullable(),
  isDangerousOccurrence: z.boolean().nullable(),
  injuryClassification: z.object({
    isFatality: z.boolean().nullable(),
    isPermanentDisability: z.boolean().nullable(),
    isLostTimeIncident: z.boolean().nullable(),
    isMedicalTreatment: z.boolean().nullable(),
    isFirstAid: z.boolean().nullable(),
  }),

  // Incident Reviewer
  incidentReviewer: z.string().min(1, "Incident reviewer is required"),

  // Optional fields from original form
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),
  incidentCategory: z.string().optional(),
  circumstances: z.string().optional(),
  workplaceActivity: z.string().optional(),
  riskCategories: z.array(z.string()).optional(),
  photos: z.array(z.any()).optional(),
  impactAssessment: z.object({
    injury: z.string().optional(),
    environmentalDamage: z.string().optional(),
    productionLoss: z.string().optional(),
    reputationalDamage: z.string().optional(),
  }).optional(),

  reportToAuthorities: z.boolean().default(false),
  authorityReportDetails: z.string().optional(),

  // Final confirmation
  confirmAccuracy: z.boolean().refine(val => val === true),
});

export type IncidentFormValues = z.infer<typeof incidentFormSchema>;

// New comprehensive incident form schema matching the API
export const newIncidentFormSchema = z.object({
  // Basic incident information
  title: z.string().min(1, "Incident title is required"),
  description: z.string().min(1, "Description is required"),
  incidentDate: z.string().min(1, "Incident date is required"),
  date: z.string().optional(),

  // Location hierarchy IDs
  locationOneId: z.string().optional(),
  locationTwoId: z.string().optional(),
  locationThreeId: z.string().optional(),
  locationFourId: z.string().optional(),
  locationFiveId: z.string().optional(),
  locationSixId: z.string().optional(),

  // Incident classification strings
  IncidentCategory: z.string().optional(),
  dangerousOccurance: z.string().optional(),
  fatality: z.string().optional(),
  injury: z.string().optional(),
  lostTime: z.string().optional(),
  medicalTreatment: z.string().optional(),
  firstAid: z.string().optional(),
  lossOfConscious: z.string().optional(),
  actualImpact: z.string().optional(),
  potentialImpact: z.string().optional(),
  reportAuthority: z.string().optional(),
  authorityName: z.string().optional(),
  propertyDamage: z.string().optional(),
  propertyDamageDetails: z.string().optional(),
  maskId: z.string().optional(),
  stopWorkOrder: z.string().optional(),
  workRelatedDetails: z.string().optional(),
  incidentData: z.string().optional(),
  created: z.string().optional(),
  classification: z.string().optional(),
  incidentOwner: z.string().optional(),
  investigationRemarks: z.string().optional(),
  status: z.string().optional(),
  informationStep: z.string().optional(),
  investigationStep: z.string().optional(),
  riskControl: z.string().optional(),

  // Boolean flags
  isWorkRelated: z.boolean().default(false),
  isInjury: z.boolean().default(false),
  isFirstAid: z.boolean().default(false),
  isPersonInjured: z.boolean().default(false),
  isMedicalTreatment: z.boolean().default(false),
  isControlMeasure: z.boolean().default(false),
  isRiskAssessment: z.boolean().default(false),
  isMedicalLeave: z.boolean().default(false),
  investigationStatus: z.boolean().default(false),
  triggerInvestigationStatus: z.boolean().default(false),
  active: z.boolean().default(true),

  // File upload arrays
  uploads: z.array(z.string()).optional(),
  evidence: z.array(z.string()).optional(),
  additionalDocuments: z.array(z.string()).optional(),

  // Environmental and condition IDs
  lightingId: z.string().optional(),
  surfaceTypeId: z.string().optional(),
  surfaceConditionId: z.string().optional(),
  riskCategoryId: z.string().optional(),
  incidentUnderlyingCauseId: z.string().optional(),
  incidentUnderlyingCauseTypeId: z.string().optional(),
  incidentUnderlyingCauseDescriptionId: z.string().optional(),
  incidentRootCauseTypeId: z.string().optional(),
  incidentRootCauseDescriptionId: z.string().optional(),
  weatherConditionId: z.string().optional(),
  workActivityId: z.string().optional(),
  incidentCircumstanceCategoryId: z.string().optional(),
  incidentCircumstanceDescriptionId: z.string().optional(),
  incidentCircumstanceTypeId: z.string().optional(),

  // User IDs
  userId: z.string().optional(),
  reviewerId: z.string().optional(),
  investigatorId: z.string().optional(),
  incidentOwnerId: z.string().optional(),

  // Confirmation
  confirmAccuracy: z.boolean().default(false),
});

export type NewIncidentFormValues = z.infer<typeof newIncidentFormSchema>;
