import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getToken, isTokenExpired, refreshToken } from '@/lib/utils';

const TokenQuickTest = () => {
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  useEffect(() => {
    const checkToken = () => {
      console.log('🔍 TokenQuickTest: Checking token...');

      const token = refreshToken(); // Force refresh to get latest token
      console.log('🔑 TokenQuickTest: Token result:', token ? token.substring(0, 50) + '...' : 'UNDEFINED');
      
      if (token) {
        const expired = isTokenExpired(token);
        console.log('🔑 TokenQuickTest: Token expired?', expired);
        
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const expirationDate = new Date(payload.exp * 1000);
          
          setTokenInfo({
            hasToken: true,
            isExpired: expired,
            expirationDate: expirationDate.toLocaleString(),
            username: payload.username || payload.sub,
            tokenPreview: token.substring(0, 100) + '...'
          });
        } catch (error) {
          setTokenInfo({
            hasToken: true,
            error: 'Failed to decode token',
            tokenPreview: token.substring(0, 100) + '...'
          });
        }
      } else {
        setTokenInfo({
          hasToken: false,
          error: 'No token available'
        });
      }
    };

    checkToken();
  }, []);

  const testApiCall = async () => {
    try {
      console.log('🧪 Testing API call with current token...');
      const token = getToken();
      
      if (!token) {
        alert('❌ No token available for API call');
        return;
      }

      const response = await fetch('https://dev.stt-user.acuizen.com/users/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ API call successful:', data);
        alert('✅ API call successful! Check console for details.');
      } else {
        const errorText = await response.text();
        console.error('❌ API call failed:', response.status, errorText);
        alert(`❌ API call failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('❌ API call error:', error);
      alert('❌ API call error: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Quick Token Test
          <div className="flex gap-2">
            <Button onClick={() => { checkToken(); }} variant="outline" size="sm">
              Refresh Token
            </Button>
            <Button onClick={testApiCall} variant="outline" size="sm">
              Test API Call
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {tokenInfo ? (
          <div className="space-y-3">
            <div className={`p-3 rounded border ${tokenInfo.hasToken ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200'}`}>
              <div className="font-medium">
                {tokenInfo.hasToken ? '🔑 Token Available' : '❌ No Token'}
              </div>
              
              {tokenInfo.error && (
                <div className="text-red-700 text-sm mt-1">{tokenInfo.error}</div>
              )}
              
              {tokenInfo.hasToken && !tokenInfo.error && (
                <div className="space-y-1 text-sm mt-2">
                  <div className={`font-medium ${tokenInfo.isExpired ? 'text-red-700' : 'text-green-700'}`}>
                    Status: {tokenInfo.isExpired ? '🚫 EXPIRED' : '✅ Valid'}
                  </div>
                  <div><strong>Expires:</strong> {tokenInfo.expirationDate}</div>
                  <div><strong>Username:</strong> {tokenInfo.username}</div>
                </div>
              )}
            </div>

            {tokenInfo.tokenPreview && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <div className="font-medium text-sm mb-1">Token Preview:</div>
                <code className="text-xs break-all">{tokenInfo.tokenPreview}</code>
              </div>
            )}
          </div>
        ) : (
          <div className="text-gray-600">Loading token information...</div>
        )}
      </CardContent>
    </Card>
  );
};

export default TokenQuickTest;
