import React, { forwardRef } from 'react';
import { Incident } from '@/contexts/IncidentContext';
import { format } from 'date-fns';
import { AlertTriangle, CheckCircle, Info, MapPin, User, FileText, Microscope, Shield } from 'lucide-react';
import { getSeverityLevel, getSeverityColorClass } from '@/utils/pdfUtils';

interface IncidentReportPDFProps {
  incident: Incident;
}

const IncidentReportPDF = forwardRef<HTMLDivElement, IncidentReportPDFProps>(
  ({ incident }, ref) => {
    // Helper function to format boolean values
    const formatBoolean = (value: boolean | null): string => {
      if (value === null) return 'Not Specified';
      return value ? 'Yes' : 'No';
    };

    // Get severity level
    const severityLevel = getSeverityLevel(incident);
    const severityColorClass = getSeverityColorClass(severityLevel);

    return (
      <div 
        ref={ref} 
        className="bg-white p-8 max-w-[210mm] mx-auto shadow-none"
        style={{ fontFamily: 'Arial, sans-serif' }}
      >
        {/* Header */}
        <div className="flex justify-between items-center border-b pb-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Incident Report</h1>
            <p className="text-gray-500">Generated on {format(new Date(), 'PPP')}</p>
          </div>
          <div className="text-right">
            <p className="font-bold text-gray-900">{incident.id}</p>
            <p className="text-gray-500">Status: <span className="text-green-600 font-medium">Closed</span></p>
          </div>
        </div>

        {/* Incident Summary */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Info className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-blue-600">Incident Summary</h2>
          </div>
          <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
            <h3 className="font-bold text-lg mb-2">{incident.incidentTitle || incident.description}</h3>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Date & Time</p>
                <p className="font-medium">{format(incident.incidentDate, 'PPP \'at\' p')}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Incident Type</p>
                <p className="font-medium">{incident.incidentType}</p>
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500">Description</p>
              <p className="font-medium whitespace-pre-wrap">{incident.description}</p>
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <MapPin className="h-5 w-5 text-green-600" />
            <h2 className="text-xl font-bold text-green-600">Location Information</h2>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Country</p>
              <p className="font-medium">{incident.locationCountry || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">City</p>
              <p className="font-medium">{incident.locationCity || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Business Unit</p>
              <p className="font-medium">{incident.locationBusinessUnit || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Project/DC Ops</p>
              <p className="font-medium">{incident.locationProject || 'Not specified'}</p>
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-md mt-4">
            <p className="text-sm text-gray-500">Location Details</p>
            <p className="font-medium">{incident.locationDetails || 'Not specified'}</p>
          </div>
        </div>

        {/* Classification */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-amber-600" />
            <h2 className="text-xl font-bold text-amber-600">Incident Classification</h2>
          </div>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Work Related?</p>
              <p className="font-medium">{formatBoolean(incident.isWorkRelated)}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Loss of Consciousness?</p>
              <p className="font-medium">{formatBoolean(incident.lossOfConsciousness)}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Dangerous Occurrence?</p>
              <p className="font-medium">{formatBoolean(incident.isDangerousOccurrence)}</p>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <p className="text-sm text-gray-500 mb-2">Injury Classification</p>
            <div className="grid grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${incident.injuryClassification?.isFatality ? 'bg-red-500' : 'bg-gray-200'}`}></div>
                <p className="text-sm font-medium">Fatality: {formatBoolean(incident.injuryClassification?.isFatality)}</p>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${incident.injuryClassification?.isPermanentDisability ? 'bg-red-500' : 'bg-gray-200'}`}></div>
                <p className="text-sm font-medium">Permanent Disability: {formatBoolean(incident.injuryClassification?.isPermanentDisability)}</p>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${incident.injuryClassification?.isLostTimeIncident ? 'bg-amber-500' : 'bg-gray-200'}`}></div>
                <p className="text-sm font-medium">Lost Time Incident: {formatBoolean(incident.injuryClassification?.isLostTimeIncident)}</p>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${incident.injuryClassification?.isMedicalTreatment ? 'bg-amber-400' : 'bg-gray-200'}`}></div>
                <p className="text-sm font-medium">Medical Treatment: {formatBoolean(incident.injuryClassification?.isMedicalTreatment)}</p>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full ${incident.injuryClassification?.isFirstAid ? 'bg-green-500' : 'bg-gray-200'}`}></div>
                <p className="text-sm font-medium">First Aid: {formatBoolean(incident.injuryClassification?.isFirstAid)}</p>
              </div>
            </div>
          </div>

          {/* Severity Level */}
          <div className={`p-4 rounded-md border ${severityColorClass}`}>
            <h3 className="font-bold text-base flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Incident Classification Result
            </h3>
            <p className="mt-2 font-medium">
              Final Classification: {severityLevel}
            </p>
          </div>
        </div>

        {/* Investigation Details */}
        {incident.investigationDetails && (
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Microscope className="h-5 w-5 text-purple-600" />
              <h2 className="text-xl font-bold text-purple-600">Investigation Details</h2>
            </div>
            
            {/* Root Cause Analysis */}
            {incident.investigationDetails.rootCause && (
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <p className="text-sm text-gray-500">Root Cause Analysis</p>
                <p className="font-medium whitespace-pre-wrap">{incident.investigationDetails.rootCause}</p>
              </div>
            )}
            
            {/* Corrective Actions */}
            {incident.investigationDetails.correctiveActions && (
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <p className="text-sm text-gray-500">Corrective Actions</p>
                <p className="font-medium whitespace-pre-wrap">{incident.investigationDetails.correctiveActions}</p>
              </div>
            )}
            
            {/* Preventive Measures */}
            {incident.investigationDetails.preventiveMeasures && (
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-sm text-gray-500">Preventive Measures</p>
                <p className="font-medium whitespace-pre-wrap">{incident.investigationDetails.preventiveMeasures}</p>
              </div>
            )}
          </div>
        )}

        {/* Control Measures */}
        {incident.controlMeasures && incident.controlMeasures.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="h-5 w-5 text-indigo-600" />
              <h2 className="text-xl font-bold text-indigo-600">Control Measures</h2>
            </div>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full text-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left">Description</th>
                    <th className="px-4 py-2 text-left">Due Date</th>
                    <th className="px-4 py-2 text-left">Person Responsible</th>
                  </tr>
                </thead>
                <tbody>
                  {incident.controlMeasures.map((measure, index) => (
                    <tr key={index} className="border-t">
                      <td className="px-4 py-2">{measure.description}</td>
                      <td className="px-4 py-2">
                        {measure.dueDate ? format(new Date(measure.dueDate), 'PPP') : 'N/A'}
                      </td>
                      <td className="px-4 py-2">{measure.personResponsible || 'N/A'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Incident Management */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-teal-600" />
            <h2 className="text-xl font-bold text-teal-600">Incident Management</h2>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Reported By</p>
              <p className="font-medium">{incident.reportedBy || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Reviewed By</p>
              <p className="font-medium">{incident.reviewedBy || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Incident Owner</p>
              <p className="font-medium">{incident.incidentOwner || 'Not specified'}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Reported At</p>
              <p className="font-medium">{format(incident.reportedAt, 'PPP')}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-500">Investigation Status</p>
              <p className="font-medium">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Completed
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 pt-4 border-t text-center text-gray-500 text-sm">
          <p>This is an official incident report document. Generated on {format(new Date(), 'PPP')} at {format(new Date(), 'HH:mm')}</p>
          <p className="mt-1">Confidential - For internal use only</p>
        </div>
      </div>
    );
  }
);

IncidentReportPDF.displayName = 'IncidentReportPDF';

export default IncidentReportPDF;
