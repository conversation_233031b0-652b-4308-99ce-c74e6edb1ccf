import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getToken, isTokenExpired } from '@/lib/utils';

const TokenDebugTest = () => {
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  const analyzeToken = () => {
    const token = getToken();
    
    if (!token) {
      setTokenInfo({
        hasToken: false,
        error: 'No token available'
      });
      return;
    }

    try {
      // Decode the token
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < currentTime;
      const expirationDate = new Date(payload.exp * 1000);
      const timeUntilExpiry = payload.exp - currentTime;

      setTokenInfo({
        hasToken: true,
        token: token.substring(0, 100) + '...',
        payload,
        isExpired,
        expirationDate: expirationDate.toLocaleString(),
        timeUntilExpiry: isExpired ? 0 : timeUntilExpiry,
        timeUntilExpiryFormatted: isExpired ? 'Expired' : `${Math.floor(timeUntilExpiry / 3600)}h ${Math.floor((timeUntilExpiry % 3600) / 60)}m`,
        username: payload.username || payload.sub,
        clientId: payload.client_id,
        scope: payload.scope
      });
    } catch (error) {
      setTokenInfo({
        hasToken: true,
        error: 'Failed to decode token: ' + (error instanceof Error ? error.message : 'Unknown error'),
        token: token.substring(0, 100) + '...'
      });
    }
  };

  useEffect(() => {
    analyzeToken();
  }, []);

  const testTokenWithAPI = async () => {
    try {
      const response = await fetch('https://dev.stt-user.acuizen.com/users/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Token is valid! User data:', data);
        alert('✅ Token is valid! Check console for user data.');
      } else {
        console.error('❌ Token validation failed:', response.status, await response.text());
        alert(`❌ Token validation failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Token test error:', error);
      alert('❌ Token test failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Token Debug & Validation
          <div className="flex gap-2">
            <Button onClick={analyzeToken} variant="outline">
              Refresh Token Info
            </Button>
            <Button onClick={testTokenWithAPI} variant="outline">
              Test Token with API
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {tokenInfo ? (
          <div className="space-y-4">
            <div className={`p-4 rounded border ${tokenInfo.hasToken ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200'}`}>
              <div className="font-medium mb-2">
                {tokenInfo.hasToken ? '🔑 Token Available' : '❌ No Token'}
              </div>
              
              {tokenInfo.error && (
                <div className="text-red-700 text-sm">{tokenInfo.error}</div>
              )}
              
              {tokenInfo.hasToken && !tokenInfo.error && (
                <div className="space-y-2 text-sm">
                  <div className={`font-medium ${tokenInfo.isExpired ? 'text-red-700' : 'text-green-700'}`}>
                    Status: {tokenInfo.isExpired ? '🚫 EXPIRED' : '✅ Valid'}
                  </div>
                  
                  <div>
                    <strong>Expires:</strong> {tokenInfo.expirationDate}
                  </div>
                  
                  <div>
                    <strong>Time until expiry:</strong> {tokenInfo.timeUntilExpiryFormatted}
                  </div>
                  
                  <div>
                    <strong>Username:</strong> {tokenInfo.username}
                  </div>
                  
                  <div>
                    <strong>Client ID:</strong> {tokenInfo.clientId}
                  </div>
                  
                  <div>
                    <strong>Scope:</strong> {tokenInfo.scope}
                  </div>
                </div>
              )}
            </div>

            {tokenInfo.hasToken && (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded">
                <div className="font-medium mb-2">Token (first 100 chars):</div>
                <code className="text-xs break-all">{tokenInfo.token}</code>
              </div>
            )}

            {tokenInfo.payload && (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded">
                <div className="font-medium mb-2">Token Payload:</div>
                <pre className="text-xs overflow-auto max-h-64">
                  {JSON.stringify(tokenInfo.payload, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ) : (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Click "Refresh Token Info" to analyze the current token</div>
          </div>
        )}

        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
          <div className="text-yellow-800 font-medium mb-2">💡 Token Issues & Solutions:</div>
          <div className="text-yellow-700 text-sm space-y-1">
            <div>• If token is expired, you need to get a new one from your authentication system</div>
            <div>• If no token in URL, make sure you're accessing the app with ?access_token=YOUR_TOKEN</div>
            <div>• Update the hardcoded token in API_CONFIG.JWT_TOKEN if needed</div>
            <div>• Check that the token has the correct permissions for the API endpoints</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TokenDebugTest;
