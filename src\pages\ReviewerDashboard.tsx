import { useState, useEffect } from "react";
import { AlertCircle, Search, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useUser } from "@/contexts/UserContext";
import { useIncidents, Incident } from "@/contexts/IncidentContext";
import IncidentActionDialog from "@/components/IncidentActionDialog";
import IncidentInvestigationView from "@/components/IncidentInvestigationView";
import InvestigationStatusDialog from "@/components/InvestigationStatusDialog";
import AllIncidentsTable from "@/components/AllIncidentsTable";
import DataTable from "@/components/DataTable";
import ReviewerActionsDebug from "@/components/ReviewerActionsDebug";
import { format } from "date-fns";
import { defaultPath } from "@/lib/paths";
import { toast } from "sonner";
import { ApiService } from "@/lib/utils";

const ReviewerDashboard = () => {
  const { userName } = useUser();
  const { incidents, updateIncident, getIncidentsRequiringAction, getReviewerActionIncidents } = useIncidents();
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInvestigationViewOpen, setIsInvestigationViewOpen] = useState(false);
  const [isInvestigationStatusDialogOpen, setIsInvestigationStatusDialogOpen] = useState(false);
  const [reviewerActionIncidents, setReviewerActionIncidents] = useState<Incident[]>([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);
  const [hasLoadedActions, setHasLoadedActions] = useState(false);

  // Filter incidents that need review (My Actions) - fallback
  const myActionIncidents = getIncidentsRequiringAction(userName, 'reviewer');

  // Manual refresh function for debugging
  const handleRefreshActions = async () => {
    console.log('🔄 Manual refresh of reviewer action incidents...');
    setIsLoadingActions(true);
    setHasLoadedActions(false);

    try {
      const apiIncidents = await getReviewerActionIncidents();
      
      setReviewerActionIncidents(apiIncidents);

      if (apiIncidents.length === 0) {
        console.log('ℹ️ No reviewer action incidents found from API, using fallback data');
        setReviewerActionIncidents(myActionIncidents);
      }

      toast.success(`Loaded ${apiIncidents.length} incidents from API`);
    } catch (error) {
      console.error('❌ Manual refresh failed:', error);
      setReviewerActionIncidents(myActionIncidents);
      toast.error('Failed to refresh action incidents');
    } finally {
      setIsLoadingActions(false);
      setHasLoadedActions(true);
    }
  };

  // Load reviewer action incidents from /actions API
  useEffect(() => {
    // Prevent repeated calls - only load once
    if (hasLoadedActions) {
      console.log('🚫 Reviewer actions already loaded, skipping API call');
      return;
    }

    const loadReviewerActions = async () => {
      console.log('🔄 Starting to load reviewer action incidents from /actions/get/INCIDENT API...');
      setIsLoadingActions(true);
      setHasLoadedActions(true);

      try {
        const apiIncidents = await getReviewerActionIncidents();
        console.log('✅ Successfully loaded reviewer action incidents:', apiIncidents);
        setReviewerActionIncidents(apiIncidents);

        if (apiIncidents.length === 0) {
          console.log('ℹ️ No reviewer action incidents found from API, using fallback data');
          setReviewerActionIncidents(myActionIncidents);
        }
      } catch (error) {
        console.error('❌ Error loading reviewer action incidents:', error);
        // Fallback to context data if API fails
        setReviewerActionIncidents(myActionIncidents);
        toast.error('Failed to load action incidents from API, using cached data');
        // Reset the flag so we can retry later
        setHasLoadedActions(false);
      } finally {
        setIsLoadingActions(false);
      }
    };

    loadReviewerActions();
  }, []); // Empty dependency array to run only once on mount

  // Filter incidents that are under investigation
  const underInvestigationIncidents = incidents.filter(
    (incident) => incident.status === "investigation" || incident.status === "Under Investigation"
  );


  // Helper functions for handling incidents
  const handleAction = (incidentId: string) => {
    console.log('🔍 Looking for incident with ID:', incidentId);
    console.log('🔍 Available reviewer action incidents:', reviewerActionIncidents);
    console.log('🔍 Available context incidents:', incidents);

    // First try to find the incident in reviewer action incidents (from API)
    let incident = reviewerActionIncidents.find(inc => inc.id === incidentId);

    // If not found, try in the context incidents (fallback)
    if (!incident) {
      incident = incidents.find(inc => inc.id === incidentId);
    }

    // If still not found, try in myActionIncidents (fallback)
    if (!incident) {
      incident = myActionIncidents.find(inc => inc.id === incidentId);
    }

    console.log('🔍 Found incident:', incident);

    if (incident) {
      setSelectedIncident(incident);
      setIsActionDialogOpen(true);
      console.log('✅ Action dialog should be open now');
    } else {
      console.error('❌ Incident not found with ID:', incidentId);
      console.error('❌ Available IDs in reviewerActionIncidents:', reviewerActionIncidents.map(inc => inc.id));
      console.error('❌ Available IDs in context incidents:', incidents.map(inc => inc.id));
      console.error('❌ Available IDs in myActionIncidents:', myActionIncidents.map(inc => inc.id));
      toast.error('Incident not found. Please refresh the page and try again.');

      // Fallback: Try to open dialog with a dummy incident for testing
      console.log('🔧 Attempting fallback with first available incident...');
      const fallbackIncident = reviewerActionIncidents[0] || myActionIncidents[0] || incidents[0];
      if (fallbackIncident) {
        console.log('🔧 Using fallback incident:', fallbackIncident);
        setSelectedIncident(fallbackIncident);
        setIsActionDialogOpen(true);
      }
    }
  };

  const handleView = (incident: Incident) => {
    setSelectedIncident(incident);
    setIsInvestigationViewOpen(true);
  };

  const handleInvestigation = (incidentId: string, currentStatus: string) => {
    // Find the incident and open the investigation status dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsInvestigationStatusDialogOpen(true);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this incident?")) {
      try {
        console.log(`🗑️ Deleting incident with ID: ${id}`);
        await ApiService.deleteIncident(id);

        toast.success("Incident deleted successfully");
        console.log(`✅ Incident ${id} deleted successfully`);
      } catch (error) {
        console.error(`❌ Failed to delete incident ${id}:`, error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        toast.error(`Failed to delete incident: ${errorMessage}`);
      }
    }
  };

  // Handle saving investigation assignment
  const handleInvestigationAssignment = (data: { leadInvestigator: string; remarks: string }) => {
    if (!selectedIncident) return;

    // Update the incident with lead investigator and remarks
    updateIncident(selectedIncident.id, {
      leadInvestigator: data.leadInvestigator,
      investigationRemarks: data.remarks,
      investigationStatus: 'in-progress' // Set status to in-progress when assigned
    });

    toast.success(`Investigation assigned for incident ${selectedIncident.id}`, {
      description: `Lead investigator assigned successfully`,
    });
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8 animate-in fade-in duration-500">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Reviewer Dashboard</h1>
          <p className="text-muted-foreground">Review and process incident reports</p>
        </div>
        <Button
          onClick={handleRefreshActions}
          disabled={isLoadingActions}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoadingActions ? 'animate-spin' : ''}`} />
          {isLoadingActions ? 'Loading...' : 'Refresh Actions'}
        </Button>
      </div>

      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Tabs defaultValue="my-actions" className="w-full">
          <div className="bg-muted/30 border-b">
            <TabsList className="w-full justify-start h-14 bg-transparent p-0">
              <TabsTrigger value="my-actions" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                My Actions <span className="ml-1 bg-amber-100 text-amber-800 rounded-full px-2 py-0.5 text-xs">{reviewerActionIncidents.length > 0 ? reviewerActionIncidents.length : myActionIncidents.length}</span>
              </TabsTrigger>
              <TabsTrigger value="all-incidents" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                All Incidents
              </TabsTrigger>
              <TabsTrigger value="under-investigation" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                Under Investigation <span className="ml-1 bg-blue-100 text-blue-800 rounded-full px-2 py-0.5 text-xs">{underInvestigationIncidents.length}</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="my-actions" className="animate-in fade-in-50 duration-300 p-6">
            {/* Debug component for testing API */}
            <div className="mb-6">
              <ReviewerActionsDebug />
            </div>

            {isLoadingActions ? (
              <div className="p-8 text-center border rounded-md">
                <h3 className="text-lg font-medium mb-2">Loading your action items...</h3>
                <p className="text-muted-foreground">Fetching incidents that require your attention from /actions/get/INCIDENT API.</p>
              </div>
            ) : reviewerActionIncidents.length === 0 && myActionIncidents.length === 0 ? (
              <div className="p-8 text-center border rounded-md">
                <h3 className="text-lg font-medium mb-2">No incidents pending review</h3>
                <p className="text-muted-foreground">All reported incidents have been reviewed.</p>
                <div className="mt-4 text-sm text-muted-foreground">
                  <p>API incidents: {reviewerActionIncidents.length}</p>
                  <p>Fallback incidents: {myActionIncidents.length}</p>
                  <p>Has loaded: {hasLoadedActions ? 'Yes' : 'No'}</p>
                </div>
              </div>
            ) : (
              <>
                <div className="mb-4 text-sm text-muted-foreground">
                  <p>Showing {reviewerActionIncidents.length > 0 ? reviewerActionIncidents.length : myActionIncidents.length} incidents
                     ({reviewerActionIncidents.length > 0 ? 'from API' : 'from fallback'})</p>
                </div>
                <DataTable
                  data={reviewerActionIncidents.length > 0 ? reviewerActionIncidents : myActionIncidents}
                  onView={handleView}
                  onEdit={handleAction}
                  onDelete={() => {}}
                  context="my-actions"
                />
              </>
            )}
          </TabsContent>

          <TabsContent value="under-investigation" className="animate-in fade-in-50 duration-300 p-6">
            <div className="rounded-md border">
              {underInvestigationIncidents.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-muted/50">
                      <TableRow>
                        <TableHead className="font-semibold">ID</TableHead>
                        <TableHead className="font-semibold">Description</TableHead>
                        <TableHead className="font-semibold">Reported By</TableHead>
                        <TableHead className="font-semibold">Date</TableHead>
                        <TableHead className="font-semibold">Status</TableHead>
                        <TableHead className="text-right font-semibold">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {underInvestigationIncidents.map((incident) => (
                        <TableRow key={incident.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{incident.maskId}</TableCell>
                          <TableCell>{incident.description}</TableCell>
                          <TableCell>{incident.reportedBy || 'Unknown'}</TableCell>
                          <TableCell>{format(incident.incidentDate, "do MMM yyyy")}</TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <AlertCircle className="w-3 h-3 mr-1" /> Under Investigation
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction(incident.id)}
                              className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                            >
                              <Search className="w-4 h-4 mr-1" />
                              Investigate
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="p-8 text-center">
                  <h3 className="text-lg font-medium mb-2">No incidents under investigation</h3>
                  <p className="text-muted-foreground">There are currently no incidents being investigated.</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="all-incidents" className="animate-in fade-in-50 duration-300 p-6">
            <AllIncidentsTable
              data={incidents}
              handleInvestigation={handleInvestigation}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Incident Action Dialog */}
      <IncidentActionDialog
        open={isActionDialogOpen}
        onOpenChange={setIsActionDialogOpen}
        incident={selectedIncident}
        userRole="reviewer"
        onActionComplete={(updatedIncident) => {
          console.log("Action completed in ReviewerDashboard:", updatedIncident);
          // Use the updateIncident function from the context to update the incident
          updateIncident(updatedIncident.id, updatedIncident);
          // Clear the selected incident
          setSelectedIncident(null);
          // Close the dialog
          setIsActionDialogOpen(false);
        }}
      />

      {/* Incident Investigation View */}
      <IncidentInvestigationView
        open={isInvestigationViewOpen}
        onOpenChange={setIsInvestigationViewOpen}
        incident={selectedIncident}
      />

      {/* Investigation Status Dialog */}
      <InvestigationStatusDialog
        open={isInvestigationStatusDialogOpen}
        onOpenChange={setIsInvestigationStatusDialogOpen}
        incident={selectedIncident}
        onSave={handleInvestigationAssignment}
        onStartInvestigation={(data) => {
          // Handle starting comprehensive investigation
          console.log("Starting comprehensive investigation with data:", data);

          // Update the incident with lead investigator and start comprehensive investigation
          if (selectedIncident) {
            const updatedIncident = {
              ...selectedIncident,
              leadInvestigator: data.leadInvestigator,
              investigationRemarks: data.remarks,
              status: 'investigation' as const, // Change status to investigation
              investigationStatus: 'in-progress' as const,
              workflowStage: 'investigation' as const,
              stage: 'Investigation in Progress',
              requiresAction: false, // Remove from My Actions since it's now under investigation
              comprehensiveInvestigationStarted: true,
              comprehensiveInvestigationStartedAt: new Date(),
              comprehensiveInvestigationStartedBy: data.leadInvestigator,
            };

            // Update the incident in the context
            updateIncident(selectedIncident.id, updatedIncident);

            // Clear the selected incident
            setSelectedIncident(null);

            // Show success message
            toast.success("Comprehensive Investigation Started!", {
              description: `Lead investigator ${data.leadInvestigator} has been assigned and comprehensive investigation has started.`,
            });
          }
        }}
      />
    </div>
  );
};

export default ReviewerDashboard;
