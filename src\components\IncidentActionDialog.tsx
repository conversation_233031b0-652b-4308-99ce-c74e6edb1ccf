import React, { useState } from "react";
import { X, Upload, <PERSON>ert<PERSON>riangle, Info, Check, FileText, HelpCircle, Microscope } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import WorkflowStepIndicator from "./WorkflowStepIndicator";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";

import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { ApiService, cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import PhotoUpload from "./PhotoUpload";
import InvestigationAnalysisForm from "./InvestigationAnalysisForm";
import ComprehensiveInvestigationForm from "./ComprehensiveInvestigationForm";
import { cities, businessUnits, projectOptions, leadInvestigators } from "@/utils/formData";
import { useUser } from "@/contexts/UserContext";

// Removed unused select function

// Helper function to get incident type label
const getIncidentTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    "fall": "Fall",
    "slip": "Slip",
    "nearMiss": "Near Miss",
    "injury": "Injury",
    "environmental": "Environmental",
    "health": "Health",
    "safety": "Safety",
    "propertyDamage": "Property Damage",
    "security": "Security",
    "quality": "Quality",
    "fire": "Fire",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "vehicle": "Vehicle",
    "other": "Other"
  };

  return typeMap[type] || type;
};

// Helper function to get incident category label
const getIncidentCategoryLabel = (category: string, incidentTypes: any[] = []): string => {
  // First try to find the label from API data
  const type = incidentTypes.find(t => t.id === category);
  if (type) {
    return type.name;
  }

  // Fallback to hardcoded mapping if not found in API data
  const categoryMap: Record<string, string> = {
    "safety": "Safety",
    "environmental": "Environmental",
    "security": "Security",
    "quality": "Quality",
    "health": "Health",
    "fire": "Fire",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "vehicle": "Vehicle",
    "machinery": "Machinery",
    "other": "Other"
  };

  return categoryMap[category] || category;
};

// Helper function to get surface type label
const getSurfaceTypeLabel = (type: string, surfaceTypes: any[] = []): string => {
  // First try to find the label from API data
  const surfaceType = surfaceTypes.find(t => t.id === type);
  if (surfaceType) {
    return surfaceType.name;
  }

  // Fallback to hardcoded mapping if not found in API data
  const typeMap: Record<string, string> = {
    "concrete": "Concrete",
    "carpet": "Carpet",
    "tile": "Tile",
    "wood": "Wood",
    "metal": "Metal",
    "asphalt": "Asphalt",
    "grass": "Grass",
    "other": "Other"
  };

  return typeMap[type] || type;
};

// Helper function to get surface condition label
const getSurfaceConditionLabel = (condition: string, surfaceConditions: any[] = []): string => {
  // First try to find the label from API data
  const surfaceCondition = surfaceConditions.find(c => c.id === condition);
  if (surfaceCondition) {
    return surfaceCondition.name;
  }

  // Fallback to hardcoded mapping if not found in API data
  const conditionMap: Record<string, string> = {
    "dry": "Dry",
    "wet": "Wet",
    "icy": "Icy",
    "oily": "Oily/Slippery",
    "damaged": "Damaged",
    "uneven": "Uneven",
    "other": "Other"
  };

  return conditionMap[condition] || condition;
};

// Helper function to get lighting label
const getLightingLabel = (lighting: string, lightings: any[] = []): string => {
  // First try to find the label from API data
  const lightingData = lightings.find(l => l.id === lighting);
  if (lightingData) {
    return lightingData.name;
  }

  // Fallback to hardcoded mapping if not found in API data
  const lightingMap: Record<string, string> = {
    "good": "Good",
    "adequate": "Adequate",
    "poor": "Poor",
    "none": "None"
  };

  return lightingMap[lighting] || lighting;
};

// Helper function to get weather condition label
const getWeatherConditionLabel = (condition: string, weatherConditions: any[] = []): string => {
  // First try to find the label from API data
  const weatherCondition = weatherConditions.find(w => w.id === condition);
  if (weatherCondition) {
    return weatherCondition.name;
  }

  // Fallback to hardcoded mapping if not found in API data
  const conditionMap: Record<string, string> = {
    "clear": "Clear",
    "cloudy": "Cloudy",
    "rain": "Rain",
    "snow": "Snow",
    "fog": "Fog",
    "wind": "Windy",
    "storm": "Storm",
    "indoor": "Indoor (N/A)"
  };

  return conditionMap[condition] || condition;
};

// Helper function to get legal classification label
const getLegalClassificationLabel = (classification: string): string => {
  const classificationMap: Record<string, string> = {
    "Normal": "Normal",
    "Client Privilege": "Client Privilege",
  };

  return classificationMap[classification] || classification;
};

// Helper function to get lead investigator label
const getLeadInvestigatorLabel = (value: string): string => {
  const investigator = leadInvestigators.find(inv => inv.value === value);
  // If not found in hardcoded list, return the value as-is (could be a name from API)
  return investigator ? investigator.label : value;
};

// Define the validation schema for the action form
const actionFormSchema = z.object({
  // Initial incident details (editable)
  incidentTitle: z.string().min(1, "Incident title is required"),
  incidentDate: z.date(),
  incidentType: z.string(),
  incidentCategory: z.string(),
  description: z.string().min(1, "Description is required"),
  location: z.object({
    country: z.string(),
    city: z.string().optional(),
    businessUnit: z.string(),
    projectDcOps: z.string().optional(),
    levelAndLocation: z.string().optional(),
  }),

  // Injury Classification
  injuryClassification: z.object({
    isWorkRelated: z.boolean().nullable().default(null),
    lossOfConsciousness: z.boolean().nullable().default(null),
    isDangerousOccurrence: z.boolean().nullable().default(null),
    isFatality: z.boolean().nullable().default(null),
    isPermanentDisability: z.boolean().nullable().default(null),
    isLostTimeIncident: z.boolean().nullable().default(null),
    isMedicalTreatment: z.boolean().nullable().default(null),
    isFirstAid: z.boolean().nullable().default(null),
  }),

  // Potentially Serious Incident Classification
  potentiallySerious: z.object({
    couldResultInFatality: z.boolean().default(false),
    couldResultInPermanentDisability: z.boolean().default(false),
  }),

  // Additional fields from the second image
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),

  surfaceType: z.string().optional(),
  surfaceCondition: z.string().optional(),
  lighting: z.string().optional(),
  weatherCondition: z.string().optional(),

  reportableToAuthorities: z.boolean().default(false),
  reportableDetails: z.string().optional(),

  immediateActionDate: z.date().optional(),
  immediateActionTaken: z.string().optional(),

  immediateActionLegalClassification: z.string().optional(),

  photos: z.array(z.string()).default([]),
  evidence: z.array(z.string()).default([]), // Evidence images from /files API



  // Control Measures fields
  incidentGMS: z.string().optional(),
  stopWorkOrder: z.boolean().default(false),
  rootCauseAnalysis: z.string().optional(),
  controlMeasuresRequired: z.boolean().default(false),
  controlMeasuresDescription: z.string().optional(),
  controlMeasures: z.array(
    z.object({
      controlMeasures: z.string(),
      completionDate: z.date().optional(),
      personResponsible: z.string().optional(),
    })
  ).default([]),
  riskAssessmentRequired: z.boolean().default(false),
  riskAssessmentDescription: z.string().optional(),
  riskAssessments: z.array(
    z.object({
      name: z.string(),
      completionDate: z.date().optional(),
      personResponsible: z.string().optional(),
    })
  ).default([]),

  // Investigation fields
  investigationDetails: z.string().optional(),
  locationPlanPhotos: z.array(z.string()).default([]), // Array of uploaded photo file names
  detailedInvestigation: z.any().optional(),
  comprehensiveInvestigation: z.any().optional(), // For the nested investigation form data
});

type ActionFormValues = z.infer<typeof actionFormSchema>;

interface IncidentActionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incident: any | null;
  onActionComplete: (updatedIncident: any) => void;
  userRole?: 'reporter' | 'reviewer' | 'owner';
}

const IncidentActionDialog: React.FC<IncidentActionDialogProps> = ({
  open,
  onOpenChange,
  incident,
  onActionComplete,
  userRole = 'reviewer',
}) => {
  // State for incident categories from API
  const [incidentCategories, setIncidentCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  // State for incident types from API
  const [incidentTypes, setIncidentTypes] = useState<any[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(false);
  // State for location hierarchy from API
  const [locationOnes, setLocationOnes] = useState<{ id: string, name: string }[]>([]);
  const [locationTwos, setLocationTwos] = useState<{ id: string, name: string }[]>([]);
  const [locationThrees, setLocationThrees] = useState<{ id: string, name: string }[]>([]);
  const [locationFours, setLocationFours] = useState<{ id: string, name: string }[]>([]);
  const [dynamicTitles, setDynamicTitles] = useState<{ id: string, title: string, altTitle: string }[]>([]);
  // State for environmental conditions from API
  const [surfaceTypes, setSurfaceTypes] = useState<{ id: string, name: string }[]>([]);
  const [surfaceConditions, setSurfaceConditions] = useState<{ id: string, name: string }[]>([]);
  const [lightings, setLightings] = useState<{ id: string, name: string }[]>([]);
  const [weatherConditions, setWeatherConditions] = useState<{ id: string, name: string }[]>([]);
  // State for location users from API
  const [locationUsers, setLocationUsers] = useState<{ value: string, label: string }[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  // Set default tab based on workflow stage
  const getDefaultTab = () => {
    if (!incident) return "details";

    // For investigation stage, default to investigation tab
    if (incident?.applicationDetails?.status === 'Reported' && incident.stage === 'Preliminary Analysis in Progress') {
      return "investigation";
    }

    // Otherwise default to details tab
    return "details";
  };

  const [activeTab, setActiveTab] = useState(getDefaultTab());
  // State for declaration checkbox in preview tab (for reviewer only)
  const [declarationChecked, setDeclarationChecked] = useState(false);
  // Get current user information at the component level
  const { userName } = useUser();

  // Helper function to determine if detailed investigation is required
  const requiresDetailedInvestigation = (incident: any) => {
    if (!incident) return false;

    // Check if incident severity requires detailed investigation
    const highSeverityTypes = ['fatality', 'permanent-disability', 'lost-time', 'high-severity'];
    const incidentLevel = incident.incidentClassificationLevel || '';

    // Level 3, 4, or 5 incidents require detailed investigation
    if (incidentLevel.includes('Level 3') || incidentLevel.includes('Level 4') || incidentLevel.includes('Level 5')) {
      return true;
    }

    // Check for potentially serious incidents
    if (incident.potentiallySerious?.couldResultInFatality ||
      incident.potentiallySerious?.couldResultInPermanentDisability) {
      return true;
    }

    // Check for specific injury classifications that require detailed investigation
    if (incident.injuryClassification?.isFatality === true ||
      incident.injuryClassification?.isPermanentDisability === true ||
      incident.injuryClassification?.isLostTimeIncident === true) {
      return true;
    }

    // Check for dangerous occurrences
    if (incident.isDangerousOccurrence === true) {
      return true;
    }

    // Check for property damage above certain threshold or environmental incidents
    if (incident.propertyDamage === true && incident.incidentType === 'environmental') {
      return true;
    }

    return false;
  };

  // Determine the current workflow stage based on incident status
  const determineWorkflowStage = () => {
    if (!incident) return 'initial';

    // Check if comprehensive investigation is required (lead investigator assigned)
    if (incident.leadInvestigator && incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress') {
      return 'comprehensive';
    }

    // For Reporter and Reviewer roles, if incident is submitted and in Preliminary Analysis stage,
    // they should see investigation tabs to update Investigation Details
    if (incident?.applicationDetails?.stage === 'Preliminary analysis is in progress') {
      return 'investigation';
    }

    switch (incident.status) {
      case 'draft':
        return 'initial';
      case 'submitted':
        return 'review';
      case 'under-review':
        return 'review';
      case 'Under Investigation':
        // For incidents with status "investigation", check if detailed investigation is required
        if (incident.leadInvestigator || requiresDetailedInvestigation(incident)) {
          return 'comprehensive';
        }
        return 'comprehensive';
      case 'comprehensive':
        return 'comprehensive';
      case 'closed':
        return 'comprehensive';
      default:
        return 'review';
    }
  };

  const workflowStage = determineWorkflowStage();

  // Check if the initial report stage is completed
  const isInitialReportCompleted = workflowStage !== 'initial';

  // Debug logging
  console.log("IncidentActionDialog - Incident status:", incident?.status);
  console.log("IncidentActionDialog - Workflow stage:", workflowStage);
  console.log("IncidentActionDialog - Requires detailed investigation:", incident ? requiresDetailedInvestigation(incident) : false);
  console.log("IncidentActionDialog - Lead investigator:", incident?.leadInvestigator);

  // Set default values based on the incident data
  React.useEffect(() => {
    if (incident) {
      console.log("🎯 Loading incident data in IncidentActionDialog:", incident);
      console.log("🎯 ApplicationDetails in dialog:", incident.applicationDetails);
      console.log("🎯 Incident keys:", Object.keys(incident));
      console.log("🎯 Incident type:", typeof incident);
      console.log("🎯 Incident ID:", incident.id);
      console.log("🎯 Incident title:", incident.incidentTitle || incident.title);
      console.log("🎯 Incident description:", incident.description);
      console.log("🎯 Location fields in incident:", {
        locationCountry: incident.locationCountry,
        locationCity: incident.locationCity,
        locationBusinessUnit: incident.locationBusinessUnit,
        locationProject: incident.locationProject,
        locationDetails: incident.locationDetails,
        country: incident.country,
        city: incident.city,
        workplaceActivity: incident.workplaceActivity,
        projectDcOps: incident.projectDcOps,
        levelAndLocation: incident.levelAndLocation,
      });



      console.log("Actions Taken data:", {
        immediateActionDate: incident.immediateActionDate,
        immediateActionsTaken: incident.immediateActionsTaken,
        immediateActionTaken: incident.immediateActionTaken,
        legalClassification: incident.legalClassification,
        immediateActionLegalClassification: incident.immediateActionLegalClassification
      });

      // Ensure we retain all data initiated by the Reporter
      // Check if applicationDetails exists and use it to populate form fields
      const appDetails = incident.applicationDetails;
      const incidentData = appDetails?.incidentData || {};

      // Check for saved investigation data from the save API
      const investigationStep = appDetails?.investigationStep || incident.investigationStep || {};

      console.log("🎯 Using applicationDetails for form population:", appDetails);
      console.log("🎯 Using incidentData for form population:", incidentData);
      console.log("🎯 Using investigationStep for form population:", investigationStep);

      // Log specific field mappings
      console.log("🎯 Field mappings from applicationDetails:");
      console.log("  - Title:", appDetails?.title);
      console.log("  - Date:", appDetails?.date);
      console.log("  - Description:", appDetails?.description);
      console.log("  - Work Related:", appDetails?.isWorkRelated);
      console.log("  - Fatality:", appDetails?.fatality);
      console.log("  - Dangerous Occurrence:", appDetails?.dangerousOccurance);
      console.log("  - Lost Time:", appDetails?.lostTime);
      console.log("  - Medical Treatment:", appDetails?.medicalTreatment);
      console.log("  - First Aid:", appDetails?.firstAid);
      console.log("  - Loss of Consciousness:", appDetails?.lossOfConscious);
      console.log("  - Property Damage:", appDetails?.propertyDamage);
      console.log("  - Property Damage Details:", appDetails?.propertyDamageDetails);
      console.log("  - Report Authority:", appDetails?.reportAuthority);
      console.log("  - Classification:", appDetails?.classification);
      console.log("  - Incident Type ID (incidentData):", incidentData?.incidentType);
      console.log("  - Incident Circumstance Category ID:", appDetails?.incidentCircumstanceCategoryId);
      console.log("🎯 Available incident categories:", incidentCategories);

      // Map location data correctly from the incident object
      // Prioritize investigationStep, then applicationDetails location IDs, then incident location IDs, then location names
      const locationData = {
        country: investigationStep?.location?.country ||
          appDetails?.locationOneId ||
          incident.locationOneId ||
          incident.locationCountry ||
          incident.country || "",
        city: investigationStep?.location?.city ||
          appDetails?.locationTwoId ||
          incident.locationTwoId ||
          incident.locationCity ||
          incident.city || "",
        businessUnit: investigationStep?.location?.businessUnit ||
          appDetails?.locationThreeId ||
          incident.locationThreeId ||
          incident.locationBusinessUnit ||
          incident.workplaceActivity || "",
        projectDcOps: investigationStep?.location?.projectDcOps ||
          appDetails?.locationFourId ||
          incident.locationFourId ||
          incident.locationProject ||
          incident.projectDcOps || "",
        levelAndLocation: investigationStep?.location?.levelAndLocation ||
          appDetails?.locationDetails ||
          incidentData?.locationDetails ||
          incident.locationDetails ||
          incident.levelAndLocation || "",
      };

      console.log("🎯 Location data mapping:", {
        "appDetails.locationOneId": appDetails?.locationOneId,
        "appDetails.locationTwoId": appDetails?.locationTwoId,
        "appDetails.locationThreeId": appDetails?.locationThreeId,
        "appDetails.locationFourId": appDetails?.locationFourId,
        "appDetails.locationOne": appDetails?.locationOne,
        "appDetails.locationTwo": appDetails?.locationTwo,
        "appDetails.locationThree": appDetails?.locationThree,
        "appDetails.locationFour": appDetails?.locationFour,
        "appDetails.locationDetails": appDetails?.locationDetails,
        "incidentData.locationDetails": incidentData?.locationDetails,
        "incident.locationOneId": incident.locationOneId,
        "incident.locationTwoId": incident.locationTwoId,
        "incident.locationThreeId": incident.locationThreeId,
        "incident.locationFourId": incident.locationFourId,
        "incident.locationDetails": incident.locationDetails,
        "incident.levelAndLocation": incident.levelAndLocation,
        "final locationData": locationData
      });

      // Debug: Check if any location data exists
      const hasLocationData = Object.values(locationData).some(value => value && value.trim() !== "");
      console.log("🎯 Has location data:", hasLocationData);

      if (!hasLocationData) {
        console.warn("⚠️ No location data found in incident object!");
        console.log("🔍 Available incident fields:", Object.keys(incident));
        console.log("🔍 Incident data sample:", {
          id: incident.id,
          title: incident.incidentTitle,
          locationOneId: incident.locationOneId,
          locationTwoId: incident.locationTwoId,
          locationThreeId: incident.locationThreeId,
        });
      }

      // Load dependent location data if IDs are available
      // Use setTimeout to ensure the form is reset first, then load dependent data
      setTimeout(() => {
        if (locationData.country) {
          console.log("🔄 Loading location twos for:", locationData.country);
          loadLocationTwos(locationData.country);
        }
      }, 100);

      setTimeout(() => {
        if (locationData.city) {
          console.log("🔄 Loading location threes for:", locationData.city);
          loadLocationThrees(locationData.city);
        }
      }, 200);

      setTimeout(() => {
        if (locationData.businessUnit) {
          console.log("🔄 Loading location fours for:", locationData.businessUnit);
          loadLocationFours(locationData.businessUnit);
        }
      }, 300);

      console.log("🎯 About to reset form with data:", {
        incidentTitle: appDetails?.title || incidentData?.incidentTitle || incident.incidentTitle || incident.description || "",
        description: appDetails?.description || incidentData?.description || incident.description || "",
        locationData: locationData
      });

      console.log("📋 Pre-populating form with comprehensive incident data:", {
        incident: incident,
        applicationDetails: appDetails,
        incidentData: incidentData,
        investigationStep: investigationStep
      });

      form.reset({
        // Initial incident details - prioritize investigationStep, then applicationDetails data
        incidentTitle: investigationStep?.incidentTitle ||
          appDetails?.title ||
          incidentData?.incidentTitle ||
          incident.incidentTitle ||
          incident.description || "",
        incidentDate: investigationStep?.incidentDate ? new Date(investigationStep.incidentDate) :
          appDetails?.date ? new Date(appDetails.date) :
            incidentData?.incidentDate ? new Date(incidentData.incidentDate) :
              incident.incidentDate ? new Date(incident.incidentDate) : new Date(),
        // incidentTime removed - now using combined datetime
        incidentType: (() => {
          const selectedType = investigationStep?.incidentType ||
            appDetails?.incidentCircumstanceCategoryId ||
            incidentData?.incidentType ||
            incident.incidentType || "";
          console.log("🎯 Selected incident type ID:", selectedType);

          // Find and log the matching category name
          const matchingCategory = incidentCategories.find(cat => cat.id === selectedType);
          if (matchingCategory) {
            console.log("🎯 Matching category found:", matchingCategory.name);
          } else {
            console.log("🎯 No matching category found for ID:", selectedType);
          }

          return selectedType;
        })(),
        incidentCategory: investigationStep?.incidentCategory ||
          incidentData?.incidentCategory ||
          incident.incidentCategory ||
          appDetails?.incidentCircumstanceTypeId ||
          incident.incidentCircumstanceTypeId || "",
        description: investigationStep?.description ||
          appDetails?.description ||
          incidentData?.description ||
          incident.description || "",
        location: locationData,
        // Injury Classification - prioritize investigationStep, then applicationDetails data
        injuryClassification: {
          isWorkRelated: investigationStep?.injuryClassification?.isWorkRelated !== undefined ? investigationStep.injuryClassification.isWorkRelated :
            investigationStep?.isWorkRelated !== undefined ? investigationStep.isWorkRelated :
              appDetails?.isWorkRelated !== undefined ? appDetails.isWorkRelated :
                incidentData?.isWorkRelated !== undefined ? incidentData.isWorkRelated :
                  incident.isWorkRelated !== undefined ? incident.isWorkRelated :
                    // Additional API field mappings
                    appDetails?.isPersonInjured !== undefined ? appDetails.isPersonInjured :
                      appDetails?.isInjury !== undefined ? appDetails.isInjury : null,
          lossOfConsciousness: investigationStep?.injuryClassification?.lossOfConsciousness !== undefined ? investigationStep.injuryClassification.lossOfConsciousness :
            investigationStep?.lossOfConsciousness !== undefined ? investigationStep.lossOfConsciousness :
              appDetails?.lossOfConscious === "true" ? true :
                appDetails?.lossOfConscious === "false" ? false :
                  incidentData?.lossOfConsciousness !== undefined ? incidentData.lossOfConsciousness :
                    incident.lossOfConsciousness !== undefined ? incident.lossOfConsciousness : null,
          isDangerousOccurrence: investigationStep?.injuryClassification?.isDangerousOccurrence !== undefined ? investigationStep.injuryClassification.isDangerousOccurrence :
            investigationStep?.isDangerousOccurrence !== undefined ? investigationStep.isDangerousOccurrence :
              appDetails?.dangerousOccurance === "true" ? true :
                appDetails?.dangerousOccurance === "false" ? false :
                  incidentData?.isDangerousOccurrence !== undefined ? incidentData.isDangerousOccurrence :
                    incident.isDangerousOccurrence !== undefined ? incident.isDangerousOccurrence : null,
          isFatality: investigationStep?.injuryClassification?.isFatality !== undefined ? investigationStep.injuryClassification.isFatality :
            appDetails?.fatality === "true" ? true :
              appDetails?.fatality === "false" ? false :
                incidentData?.injuryClassification?.isFatality !== undefined ? incidentData.injuryClassification.isFatality :
                  incident.injuryClassification?.isFatality !== undefined ? incident.injuryClassification.isFatality : null,
          isPermanentDisability: investigationStep?.injuryClassification?.isPermanentDisability !== undefined ? investigationStep.injuryClassification.isPermanentDisability :
            incidentData?.injuryClassification?.isPermanentDisability !== undefined ? incidentData.injuryClassification.isPermanentDisability :
              incident.injuryClassification?.isPermanentDisability !== undefined ? incident.injuryClassification.isPermanentDisability : null,
          isLostTimeIncident: investigationStep?.injuryClassification?.isLostTimeIncident !== undefined ? investigationStep.injuryClassification.isLostTimeIncident :
            appDetails?.lostTime === "true" ? true :
              appDetails?.lostTime === "false" ? false :
                incidentData?.injuryClassification?.isLostTimeIncident !== undefined ? incidentData.injuryClassification.isLostTimeIncident :
                  incident.injuryClassification?.isLostTimeIncident !== undefined ? incident.injuryClassification.isLostTimeIncident : null,
          isMedicalTreatment: investigationStep?.injuryClassification?.isMedicalTreatment !== undefined ? investigationStep.injuryClassification.isMedicalTreatment :
            appDetails?.medicalTreatment === "true" ? true :
              appDetails?.medicalTreatment === "false" ? false :
                incidentData?.injuryClassification?.isMedicalTreatment !== undefined ? incidentData.injuryClassification.isMedicalTreatment :
                  incident.injuryClassification?.isMedicalTreatment !== undefined ? incident.injuryClassification.isMedicalTreatment : null,
          isFirstAid: investigationStep?.injuryClassification?.isFirstAid !== undefined ? investigationStep.injuryClassification.isFirstAid :
            appDetails?.firstAid === "true" ? true :
              appDetails?.firstAid === "false" ? false :
                incidentData?.injuryClassification?.isFirstAid !== undefined ? incidentData.injuryClassification.isFirstAid :
                  incident.injuryClassification?.isFirstAid !== undefined ? incident.injuryClassification.isFirstAid : null,
        },
        // Classification and other fields
        potentiallySerious: {
          couldResultInFatality: investigationStep?.potentiallySerious?.couldResultInFatality ||
            incident.potentiallySerious?.couldResultInFatality || false,
          couldResultInPermanentDisability: investigationStep?.potentiallySerious?.couldResultInPermanentDisability ||
            incident.potentiallySerious?.couldResultInPermanentDisability || false,
        },
        propertyDamage: investigationStep?.propertyDamage !== undefined ? investigationStep.propertyDamage :
          appDetails?.propertyDamage === "true" ? true :
            appDetails?.propertyDamage === "false" ? false :
              incidentData?.propertyDamage !== undefined ? incidentData.propertyDamage :
                incident.propertyDamage !== undefined ? incident.propertyDamage : false,
        propertyDamageDetails: investigationStep?.propertyDamageDetails ||
          appDetails?.propertyDamageDetails ||
          incidentData?.propertyDamageDetails ||
          incident.propertyDamageDetails || "",
        surfaceType: investigationStep?.surfaceType ||
          appDetails?.surfaceTypeId ||
          incident.surfaceTypeId ||
          incident.surfaceType || "",
        surfaceCondition: investigationStep?.surfaceCondition ||
          appDetails?.surfaceConditionId ||
          incident.surfaceConditionId ||
          incident.surfaceCondition || "",
        lighting: investigationStep?.lighting ||
          appDetails?.lightingId ||
          incident.lightingId ||
          incident.lighting || "",
        weatherCondition: investigationStep?.weatherCondition ||
          appDetails?.weatherConditionId ||
          incident.weatherConditionId ||
          incident.weatherCondition || "",
        reportableToAuthorities: investigationStep?.reportableToAuthorities !== undefined ? investigationStep.reportableToAuthorities :
          appDetails?.reportAuthority === "true" ? true :
            appDetails?.reportAuthority === "false" ? false :
              incidentData?.reportToAuthorities !== undefined ? incidentData.reportToAuthorities :
                incident.reportableToAuthorities !== undefined ? incident.reportableToAuthorities : false,
        reportableDetails: investigationStep?.reportableDetails ||
          appDetails?.authorityName ||
          incidentData?.authorityReportDetails ||
          incident.reportableDetails || "",
        immediateActionDate: investigationStep?.immediateActionDate ? new Date(investigationStep.immediateActionDate) :
          appDetails?.date ? new Date(appDetails.date) :
            incident.immediateActionDate ? new Date(incident.immediateActionDate) : new Date(),
        immediateActionTaken: investigationStep?.immediateActionTaken ||
          incident.immediateActionsTaken ||
          incident.immediateActionTaken || "",
        immediateActionLegalClassification: investigationStep?.immediateActionLegalClassification ||
          appDetails?.classification ||
          incident.legalClassification ||
          incident.immediateActionLegalClassification || "",
        photos: investigationStep?.photos || appDetails?.uploads || incident.photos || [], // Photos are now stored as filenames from API
        evidence: investigationStep?.evidence || appDetails?.evidence || incident.evidence || [], // Evidence images from /files API

        // Control Measures fields
        incidentGMS: investigationStep?.incidentGMS || incident.incidentGMS || "",
        stopWorkOrder: investigationStep?.stopWorkOrder !== undefined ? investigationStep.stopWorkOrder :
          appDetails?.stopWorkOrder === "true" ? true :
            appDetails?.stopWorkOrder === "false" ? false :
              incident.stopWorkOrder !== undefined ? incident.stopWorkOrder : false,
        rootCauseAnalysis: investigationStep?.rootCauseAnalysis || incident.rootCauseAnalysis || "",
        controlMeasuresRequired: investigationStep?.controlMeasuresRequired !== undefined ? investigationStep.controlMeasuresRequired :
          appDetails?.isControlMeasure !== undefined ? appDetails.isControlMeasure :
            incident.controlMeasuresRequired !== undefined ? incident.controlMeasuresRequired : false,
        controlMeasuresDescription: investigationStep?.controlMeasuresDescription || incident.controlMeasuresDescription || "",
        controlMeasures: investigationStep?.controlMeasures || incident.controlMeasures || [],
        riskAssessmentRequired: investigationStep?.riskAssessmentRequired !== undefined ? investigationStep.riskAssessmentRequired :
          appDetails?.isRiskAssessment !== undefined ? appDetails.isRiskAssessment :
            incident.riskAssessmentRequired !== undefined ? incident.riskAssessmentRequired : false,
        riskAssessmentDescription: investigationStep?.riskAssessmentDescription || incident.riskAssessmentDescription || "",
        riskAssessments: investigationStep?.riskAssessments || incident.riskAssessments || [],

        // Investigation fields
        investigationDetails: investigationStep?.investigationDetails || incident.investigationDetails || "",
        locationPlanPhotos: investigationStep?.locationPlanPhotos || incident.locationPlanPhotos || appDetails?.locationPlanPhotos || [],
        detailedInvestigation: investigationStep?.detailedInvestigation || incident.detailedInvestigation || {},
        comprehensiveInvestigation: investigationStep?.comprehensiveInvestigation || incident.comprehensiveInvestigation || {},
      });

      console.log("🎯 Form reset completed with location data:", {
        "form values after reset": form.getValues("location"),
        "locationData used": locationData,
        "form country value": form.getValues("location.country"),
        "form city value": form.getValues("location.city"),
        "form businessUnit value": form.getValues("location.businessUnit"),
        "form projectDcOps value": form.getValues("location.projectDcOps")
      });

      // Load users for the location
      if (appDetails?.locationOneId || appDetails?.locationTwoId || appDetails?.locationThreeId || appDetails?.locationFourId) {
        const locationParams = {
          locationOneId: appDetails.locationOneId,
          locationTwoId: appDetails.locationTwoId,
          locationThreeId: appDetails.locationThreeId,
          locationFourId: appDetails.locationFourId
        };
        loadLocationUsers(locationParams);
      }

      // Set the active tab based on workflow stage
      if (incident?.applicationDetails?.stage === 'Preliminary analysis is in progress') {
        // If lead investigator is assigned, show comprehensive investigation tab
        if (incident.leadInvestigator || requiresDetailedInvestigation(incident)) {
          setActiveTab("comprehensive-investigation");
        } else {
          setActiveTab("investigation");
        }
      } else if (incident.status === 'investigation') {
        // For incidents with status "investigation", check if detailed investigation is required
        if (incident.leadInvestigator || requiresDetailedInvestigation(incident)) {
          setActiveTab("comprehensive-investigation");
        } else {
          setActiveTab("investigation");
        }
      } else {
        setActiveTab("details");
      }
    }
  }, [incident]);

  // Load incident categories from API
  const loadIncidentCategories = async () => {
    try {
      setLoadingCategories(true);
      console.log("🔄 IncidentActionDialog: Loading incident categories...");
      console.log("🔗 IncidentActionDialog: Calling API endpoint: /incident-circumstance-categories");

      const response = await ApiService.getIncidentCircumstanceCategories();

      console.log("✅ IncidentActionDialog: Raw API response:", response);
      console.log("✅ IncidentActionDialog: Response type:", typeof response);
      console.log("✅ IncidentActionDialog: Is array:", Array.isArray(response));

      if (Array.isArray(response)) {
        console.log("✅ IncidentActionDialog: Categories count:", response.length);
        console.log("✅ IncidentActionDialog: First category:", response[0]);
        setIncidentCategories(response);
      } else {
        console.log("⚠️ IncidentActionDialog: Response is not an array, setting empty array");
        setIncidentCategories([]);
      }
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load incident categories:", error);
      console.error("❌ IncidentActionDialog: Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      toast.error(`Failed to load incident categories: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Fallback to hardcoded categories if API fails
      console.log("🔄 IncidentActionDialog: Using fallback incident categories");
      const fallbackCategories = [
        { id: "health", name: "Health" },
        { id: "safety", name: "Safety" }
      ];
      setIncidentCategories(fallbackCategories);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Load incident types from API
  const loadIncidentTypes = async () => {
    try {
      setLoadingTypes(true);
      console.log("🔄 IncidentActionDialog: Loading incident types...");
      console.log("🔗 IncidentActionDialog: Calling API endpoint: /incident-circumstance-types");

      const response = await ApiService.getIncidentCircumstanceTypes();

      console.log("✅ IncidentActionDialog: Raw incident types response:", response);
      console.log("✅ IncidentActionDialog: Response type:", typeof response);
      console.log("✅ IncidentActionDialog: Is array:", Array.isArray(response));

      if (Array.isArray(response)) {
        console.log("✅ IncidentActionDialog: Types count:", response.length);
        console.log("✅ IncidentActionDialog: First type:", response[0]);
        setIncidentTypes(response);
      } else {
        console.log("⚠️ IncidentActionDialog: Response is not an array, setting empty array");
        setIncidentTypes([]);
      }
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load incident types:", error);
      console.error("❌ IncidentActionDialog: Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      toast.error(`Failed to load incident types: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Fallback to hardcoded types if API fails
      console.log("🔄 IncidentActionDialog: Using fallback incident types");
      const fallbackTypes = [
        { id: "fire", name: "Fire" },
        { id: "slip", name: "Slip" },
        { id: "fall", name: "Fall" },
        { id: "electrical", name: "Electrical Hazard" },
        { id: "chemical", name: "Chemical Spill" },
        { id: "vehicle", name: "Vehicle Accident" },
        { id: "machinery", name: "Machinery" },
        { id: "tool", name: "Tool Related" },
        { id: "ergonomic", name: "Ergonomic" },
        { id: "other", name: "Other" }
      ];
      setIncidentTypes(fallbackTypes);
    } finally {
      setLoadingTypes(false);
    }
  };

  // Location loading functions
  const loadLocationOnes = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading location ones...");
      const response = await ApiService.getLocationOnes();
      console.log("✅ IncidentActionDialog: Location ones loaded:", response);
      console.log("✅ IncidentActionDialog: Location ones count:", response?.length || 0);
      setLocationOnes(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load location ones:", error);
      toast.error("Failed to load locations");
    }
  };

  const loadLocationTwos = async (locationOneId: string) => {
    try {
      console.log("🔄 IncidentActionDialog: Loading location twos for locationOneId:", locationOneId);
      const response = await ApiService.getLocationTwos(locationOneId);
      console.log("✅ IncidentActionDialog: Location twos loaded:", response);
      console.log("✅ IncidentActionDialog: Location twos count:", response?.length || 0);
      setLocationTwos(response || []);
      // Reset dependent locations
      setLocationThrees([]);
      setLocationFours([]);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load location twos:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadLocationThrees = async (locationTwoId: string) => {
    try {
      const response = await ApiService.getLocationThrees(locationTwoId);
      setLocationThrees(response || []);
      // Reset dependent locations
      setLocationFours([]);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load location threes:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadLocationFours = async (locationThreeId: string) => {
    try {
      const response = await ApiService.getLocationFours(locationThreeId);
      setLocationFours(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load location fours:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadDynamicTitles = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading dynamic titles...");
      const response = await ApiService.getDynamicTitles();
      console.log("✅ IncidentActionDialog: Dynamic titles loaded:", response);
      setDynamicTitles(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load dynamic titles:", error);
      toast.error("Failed to load dynamic titles");
    }
  };

  // Environmental conditions loading functions
  const loadSurfaceTypes = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading surface types...");
      const response = await ApiService.getSurfaceTypes();
      console.log("✅ IncidentActionDialog: Surface types loaded:", response);
      setSurfaceTypes(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load surface types:", error);
      toast.error("Failed to load surface types");
    }
  };

  const loadSurfaceConditions = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading surface conditions...");
      const response = await ApiService.getSurfaceConditions();
      console.log("✅ IncidentActionDialog: Surface conditions loaded:", response);
      setSurfaceConditions(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load surface conditions:", error);
      toast.error("Failed to load surface conditions");
    }
  };

  const loadLightings = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading lightings...");
      const response = await ApiService.getLightings();
      console.log("✅ IncidentActionDialog: Lightings loaded:", response);
      setLightings(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load lightings:", error);
      toast.error("Failed to load lightings");
    }
  };

  const loadWeatherConditions = async () => {
    try {
      console.log("🔄 IncidentActionDialog: Loading weather conditions...");
      const response = await ApiService.getWeatherConditions();
      console.log("✅ IncidentActionDialog: Weather conditions loaded:", response);
      setWeatherConditions(response || []);
    } catch (error) {
      console.error("❌ IncidentActionDialog: Failed to load weather conditions:", error);
      toast.error("Failed to load weather conditions");
    }
  };

  const loadLocationUsers = async (locationData: any) => {
    try {
      setLoadingUsers(true);
      const response = await ApiService.getAllUsersByLocation(locationData);

      // Check different possible response structures
      let userData = null;
      if (response?.data) {
        userData = response.data;
      } else if (Array.isArray(response)) {
        userData = response;
      }

      const users = userData?.map((user: any) => ({
        value: user.id,
        label: user.firstName || user.name || user.username || `User ${user.id}`
      })) || [];

      setLocationUsers(users);
    } catch (error) {
      console.error("Failed to load location users:", error);
      toast.error("Failed to load users");
      setLocationUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Helper function to get dynamic title for location levels
  const getDynamicTitle = (locationLevel: string): string => {
    const titleMapping: Record<string, string> = {
      'LocationOne': dynamicTitles.find(t => t.title === 'LocationOne')?.altTitle || 'Country',
      'LocationTwo': dynamicTitles.find(t => t.title === 'LocationTwo')?.altTitle || 'City',
      'LocationThree': dynamicTitles.find(t => t.title === 'LocationThree')?.altTitle || 'Business Unit',
      'LocationFour': dynamicTitles.find(t => t.title === 'LocationFour')?.altTitle || 'Project/DC Name',
      'LocationFive': dynamicTitles.find(t => t.title === 'LocationFive')?.altTitle || 'Level',
      'LocationSix': dynamicTitles.find(t => t.title === 'LocationSix')?.altTitle || 'Zone',
    };
    return titleMapping[locationLevel] || locationLevel;
  };

  // Load API data when dialog opens
  React.useEffect(() => {
    if (open) {
      loadIncidentCategories();
      loadIncidentTypes();
      loadLocationOnes();
      loadDynamicTitles();
      loadSurfaceTypes();
      loadSurfaceConditions();
      loadLightings();
      loadWeatherConditions();
    }
  }, [open]);

  const form = useForm<ActionFormValues>({
    resolver: zodResolver(actionFormSchema),
    defaultValues: {
      // Initial incident details
      incidentTitle: "",
      incidentDate: new Date(),
      incidentType: "",
      incidentCategory: "",
      description: "",
      location: {
        country: "",
        city: "",
        businessUnit: "",
        projectDcOps: "",
        levelAndLocation: "",
      },
      // Injury Classification
      injuryClassification: {
        isWorkRelated: null,
        lossOfConsciousness: null,
        isDangerousOccurrence: null,
        isFatality: null,
        isPermanentDisability: null,
        isLostTimeIncident: null,
        isMedicalTreatment: null,
        isFirstAid: null,
      },
      // Classification and other fields
      potentiallySerious: {
        couldResultInFatality: false,
        couldResultInPermanentDisability: false,
      },
      propertyDamage: false,
      propertyDamageDetails: "",
      surfaceType: "",
      surfaceCondition: "",
      lighting: "",
      weatherCondition: "",
      reportableToAuthorities: false,
      reportableDetails: "",
      immediateActionDate: new Date(),
      immediateActionTaken: "",
      immediateActionLegalClassification: "",
      photos: [],
      evidence: [],

      // Control Measures fields
      incidentGMS: "",
      stopWorkOrder: false,
      rootCauseAnalysis: "",
      controlMeasuresRequired: false,
      controlMeasuresDescription: "",
      controlMeasures: [],
      riskAssessmentRequired: false,
      riskAssessmentDescription: "",
      riskAssessments: [],

      // Investigation fields
      investigationDetails: "",
      locationPlanPhotos: [],
      detailedInvestigation: {},
      comprehensiveInvestigation: {},
    },
  });

  const watchPropertyDamage = form.watch("propertyDamage");
  const watchReportableToAuthorities = form.watch("reportableToAuthorities");
  const watchControlMeasures = useWatch({
    control: form.control,
    name: "controlMeasures",
    defaultValue: []
  });
  const watchRiskAssessments = useWatch({
    control: form.control,
    name: "riskAssessments",
    defaultValue: []
  });

  // Function to calculate incident classification level based on injury classification
  const calculateIncidentClassification = (injuryClassification: any) => {
    let level = "Near Miss"; // Default level

    // Check each classification level and determine the highest level selected
    if (injuryClassification.isFatality === true) {
      level = "Level 5 – Critical Incident";
    } else if (injuryClassification.isPermanentDisability === true) {
      level = "Level 4 – High Severity Incident";
    } else if (injuryClassification.isLostTimeIncident === true) {
      level = "Level 3 – LTI - Medium Severity Incident";
    } else if (injuryClassification.isMedicalTreatment === true) {
      level = "Level 2 – Low Severity Incident (MTI)";
    } else if (injuryClassification.isFirstAid === true) {
      level = "Level 1 – Very Low Severity Incident (FAI)";
    } else if (
      injuryClassification.isFatality === false &&
      injuryClassification.isPermanentDisability === false &&
      injuryClassification.isLostTimeIncident === false &&
      injuryClassification.isMedicalTreatment === false &&
      injuryClassification.isFirstAid === false
    ) {
      // Only set to Near Miss if all questions are explicitly answered as No
      level = "Near Miss";
    }

    return level;
  };

  // Function to save changes for reporters using PATCH API
  const saveReporterChanges = async () => {
    // Get current form values
    const currentValues = form.getValues();
    console.log("🔄 Saving reporter changes via PATCH API:", currentValues);

    if (!incident) {
      toast.error("No incident selected");
      return;
    }

    try {
      // Prepare data for PATCH /report-incidents/{id} API - matching API documentation
      const patchData = {
        // Basic incident information
        IncidentCategory: currentValues.incidentCategory || "",
        title: currentValues.incidentTitle || "",
        description: currentValues.description || "",
        date: currentValues.incidentDate instanceof Date ? currentValues.incidentDate.toISOString() : currentValues.incidentDate,
        incidentDate: currentValues.incidentDate instanceof Date ? currentValues.incidentDate.toISOString().split('T')[0] : currentValues.incidentDate,

        // Injury classification - mapping to API fields
        fatality: currentValues.injuryClassification.isFatality ? "true" : "false",
        injury: currentValues.injuryClassification.isMedicalTreatment || currentValues.injuryClassification.isFirstAid ? "true" : "false",
        lostTime: currentValues.injuryClassification.isLostTimeIncident ? "true" : "false",
        medicalTreatment: currentValues.injuryClassification.isMedicalTreatment ? "true" : "false",
        firstAid: currentValues.injuryClassification.isFirstAid ? "true" : "false",
        lossOfConscious: currentValues.injuryClassification.lossOfConsciousness ? "true" : "false",
        dangerousOccurance: currentValues.injuryClassification.isDangerousOccurrence ? "true" : "false",

        // Work related information
        isWorkRelated: currentValues.injuryClassification.isWorkRelated || false,
        workRelatedDetails: currentValues.injuryClassification.isWorkRelated ? "Work-related incident" : "",

        // Boolean flags for injury types
        isInjury: currentValues.injuryClassification.isMedicalTreatment || currentValues.injuryClassification.isFirstAid || false,
        isFirstAid: currentValues.injuryClassification.isFirstAid || false,
        isPersonInjured: currentValues.injuryClassification.isMedicalTreatment || currentValues.injuryClassification.isFirstAid || false,
        isMedicalTreatment: currentValues.injuryClassification.isMedicalTreatment || false,
        isMedicalLeave: currentValues.injuryClassification.isLostTimeIncident || false,

        // Potentially serious impacts
        actualImpact: currentValues.potentiallySerious.couldResultInFatality ? "High" : "Low",
        potentialImpact: currentValues.potentiallySerious.couldResultInPermanentDisability ? "High" : "Medium",

        // Property damage
        propertyDamage: currentValues.propertyDamage ? "true" : "false",
        propertyDamageDetails: currentValues.propertyDamageDetails || "",

        // Reportable to authorities
        reportAuthority: currentValues.reportableToAuthorities ? "true" : "false",
        authorityName: currentValues.reportableDetails || "",
        reportableDetails: currentValues.reportableDetails || "",

        // Immediate actions
        immediateActionDate: currentValues.immediateActionDate || "",
        immediateActionTaken: currentValues.immediateActionTaken || "",
        immediateActionLegalClassification: currentValues.immediateActionLegalClassification || "",

        // Control measures and risk assessments
        stopWorkOrder: currentValues.stopWorkOrder ? "true" : "false",
        isControlMeasure: currentValues.controlMeasuresRequired || false,
        isRiskAssessment: currentValues.riskAssessmentRequired || false,

        // Location hierarchy IDs
        locationOneId: currentValues.location.country || "",
        locationTwoId: currentValues.location.city || "",
        locationThreeId: currentValues.location.businessUnit || "",
        locationFourId: currentValues.location.projectDcOps || "",
        locationFiveId: "", // Not used in current form
        locationSixId: "", // Not used in current form

        // Environmental conditions
        lightingId: currentValues.lighting || "",
        surfaceTypeId: currentValues.surfaceType || "",
        surfaceConditionId: currentValues.surfaceCondition || "",
        weatherConditionId: currentValues.weatherCondition || "",

        // Incident classification IDs
        incidentCircumstanceCategoryId: currentValues.incidentType || "",
        incidentCircumstanceTypeId: currentValues.incidentCategory || "",

        // File uploads
        uploads: currentValues.photos || [],
        evidence: currentValues.evidence || [],
        additionalDocuments: [], // Not used in current form


        classification: calculateIncidentClassification(currentValues.injuryClassification),



      };

      console.log("📤 Sending PATCH request to /report-incidents/" + incident.id, patchData);

      // Send PATCH request to update the incident
      const response = await fetch(`https://dev.stt-user.acuizen.com/report-incidents/${incident.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(patchData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // const result = await response.json();
      // console.log("✅ PATCH response:", result);

      // Update local context with the saved data
      const updatedIncident = JSON.parse(JSON.stringify(incident));
      Object.assign(updatedIncident, currentValues);
      updatedIncident.lastSavedAt = new Date();
      updatedIncident.lastSavedBy = userName;

      onActionComplete(updatedIncident);

      toast.success("Changes saved successfully", {
        description: "Your incident data has been updated via PATCH API.",
      });

    } catch (error) {
      console.error("❌ Failed to save reporter changes:", error);
      toast.error("Failed to save changes", {
        description: "Please try again or contact support.",
      });
    }
  };

  // Function to save changes without submitting or closing the dialog
  const saveChanges = async () => {
    // Get current form values
    const currentValues = form.getValues();
    console.log("Saving changes:", currentValues);

    if (!incident) {
      toast.error("No incident selected");
      return;
    }

    // For comprehensive investigation stage, use the save investigation API
    if (workflowStage === 'comprehensive') {
      try {
        // Prepare investigation data for the API
        const investigationData = {
          investigationStep: {
            // Basic incident information
            incidentTitle: currentValues.incidentTitle,
            description: currentValues.description,
            incidentDate: currentValues.incidentDate,
            incidentType: currentValues.incidentType,
            incidentCategory: currentValues.incidentCategory,

            // Location information
            location: currentValues.location,

            // Injury classification
            injuryClassification: currentValues.injuryClassification,
            isWorkRelated: currentValues.injuryClassification.isWorkRelated,
            lossOfConsciousness: currentValues.injuryClassification.lossOfConsciousness,
            isDangerousOccurrence: currentValues.injuryClassification.isDangerousOccurrence,

            // Potentially serious
            potentiallySerious: currentValues.potentiallySerious,

            // Property damage
            propertyDamage: currentValues.propertyDamage,
            propertyDamageDetails: currentValues.propertyDamageDetails,

            // Environmental conditions
            surfaceType: currentValues.surfaceType,
            surfaceCondition: currentValues.surfaceCondition,
            lighting: currentValues.lighting,
            weatherCondition: currentValues.weatherCondition,

            // Reportable to authorities
            reportableToAuthorities: currentValues.reportableToAuthorities,
            reportableDetails: currentValues.reportableDetails,

            // Immediate actions
            immediateActionDate: currentValues.immediateActionDate,
            immediateActionTaken: currentValues.immediateActionTaken,
            immediateActionLegalClassification: currentValues.immediateActionLegalClassification,

            // Photos and evidence
            photos: currentValues.photos,
            evidence: currentValues.evidence,

            // Control measures
            incidentGMS: currentValues.incidentGMS,
            stopWorkOrder: currentValues.stopWorkOrder,
            rootCauseAnalysis: currentValues.rootCauseAnalysis,
            controlMeasuresRequired: currentValues.controlMeasuresRequired,
            controlMeasuresDescription: currentValues.controlMeasuresDescription,
            controlMeasures: currentValues.controlMeasures,

            // Risk assessments
            riskAssessmentRequired: currentValues.riskAssessmentRequired,
            riskAssessmentDescription: currentValues.riskAssessmentDescription,
            riskAssessments: currentValues.riskAssessments,

            // Investigation details
            investigationDetails: currentValues.investigationDetails,
            locationPlanPhotos: currentValues.locationPlanPhotos,
            detailedInvestigation: currentValues.detailedInvestigation,
            comprehensiveInvestigation: currentValues.comprehensiveInvestigation,
          }
        };

        console.log("📤 Sending PATCH request to save investigation data:", investigationData);

        // Call the save investigation API with PATCH method
        await ApiService.saveIncidentInvestigation(incident.id, investigationData);

        toast.success("Investigation data saved successfully", {
          description: "Your investigation details have been saved to the system.",
        });

        return; // Exit early for comprehensive stage
      } catch (error) {
        console.error("❌ Failed to save investigation data:", error);
        toast.error("Failed to save investigation data", {
          description: "Please try again or contact support.",
        });
        return;
      }
    }

    // Create a deep copy of the incident to ensure we don't lose any data
    const updatedIncident = JSON.parse(JSON.stringify(incident));
    console.log("🎯 ApplicationDetails preserved in updatedIncident (preview):", updatedIncident.applicationDetails);

    // Update the incident with the form data, ensuring we retain all existing data
    // Updated initial details
    updatedIncident.incidentTitle = currentValues.incidentTitle;
    updatedIncident.description = currentValues.description;
    updatedIncident.incidentDate = currentValues.incidentDate;
    updatedIncident.incidentType = currentValues.incidentType;
    updatedIncident.incidentCategory = currentValues.incidentCategory;

    // Map incident category ID to incidentCircumstanceTypeId for API compatibility
    updatedIncident.incidentCircumstanceTypeId = currentValues.incidentCategory;

    // Also update applicationDetails if it exists
    if (updatedIncident.applicationDetails) {
      updatedIncident.applicationDetails.incidentCircumstanceTypeId = currentValues.incidentCategory;
      // Update location IDs in applicationDetails
      updatedIncident.applicationDetails.locationOneId = currentValues.location.country;
      updatedIncident.applicationDetails.locationTwoId = currentValues.location.city;
      updatedIncident.applicationDetails.locationThreeId = currentValues.location.businessUnit;
      updatedIncident.applicationDetails.locationFourId = currentValues.location.projectDcOps;
      updatedIncident.applicationDetails.locationDetails = currentValues.location.levelAndLocation;

      // Also update incidentData within applicationDetails if it exists
      if (updatedIncident.applicationDetails.incidentData) {
        updatedIncident.applicationDetails.incidentData.locationDetails = currentValues.location.levelAndLocation;
      }

      // Update property damage information in applicationDetails
      updatedIncident.applicationDetails.propertyDamage = currentValues.propertyDamage ? "true" : "false";
      updatedIncident.applicationDetails.propertyDamageDetails = currentValues.propertyDamageDetails || "";

      // Update environmental condition IDs in applicationDetails
      updatedIncident.applicationDetails.surfaceTypeId = currentValues.surfaceType;
      updatedIncident.applicationDetails.surfaceConditionId = currentValues.surfaceCondition;
      updatedIncident.applicationDetails.lightingId = currentValues.lighting;
      updatedIncident.applicationDetails.weatherConditionId = currentValues.weatherCondition;
    }

    // Map location data to both old and new field names for compatibility
    updatedIncident.locationCountry = currentValues.location.country;
    updatedIncident.locationCity = currentValues.location.city;
    updatedIncident.locationBusinessUnit = currentValues.location.businessUnit;
    updatedIncident.locationProject = currentValues.location.projectDcOps;
    updatedIncident.locationDetails = currentValues.location.levelAndLocation;

    // Map location IDs for API compatibility
    updatedIncident.locationOneId = currentValues.location.country;
    updatedIncident.locationTwoId = currentValues.location.city;
    updatedIncident.locationThreeId = currentValues.location.businessUnit;
    updatedIncident.locationFourId = currentValues.location.projectDcOps;

    // Keep old field names for backward compatibility
    updatedIncident.country = currentValues.location.country;
    updatedIncident.city = currentValues.location.city;
    updatedIncident.workplaceActivity = currentValues.location.businessUnit;
    updatedIncident.projectDcOps = currentValues.location.projectDcOps;
    updatedIncident.levelAndLocation = currentValues.location.levelAndLocation;

    console.log("Saving Location data:", {
      country: currentValues.location.country,
      city: currentValues.location.city,
      businessUnit: currentValues.location.businessUnit,
      projectDcOps: currentValues.location.projectDcOps,
      levelAndLocation: currentValues.location.levelAndLocation
    });

    // Injury Classification - ensure we properly merge with existing data
    updatedIncident.injuryClassification = {
      ...(updatedIncident.injuryClassification || {}),
      isFatality: currentValues.injuryClassification.isFatality,
      isPermanentDisability: currentValues.injuryClassification.isPermanentDisability,
      isLostTimeIncident: currentValues.injuryClassification.isLostTimeIncident,
      isMedicalTreatment: currentValues.injuryClassification.isMedicalTreatment,
      isFirstAid: currentValues.injuryClassification.isFirstAid,
    };

    updatedIncident.isWorkRelated = currentValues.injuryClassification.isWorkRelated;
    updatedIncident.lossOfConsciousness = currentValues.injuryClassification.lossOfConsciousness;
    updatedIncident.isDangerousOccurrence = currentValues.injuryClassification.isDangerousOccurrence;

    // Classification and additional details
    updatedIncident.potentiallySerious = {
      ...(updatedIncident.potentiallySerious || {}),
      couldResultInFatality: currentValues.potentiallySerious.couldResultInFatality,
      couldResultInPermanentDisability: currentValues.potentiallySerious.couldResultInPermanentDisability,
    };

    updatedIncident.propertyDamage = currentValues.propertyDamage;
    updatedIncident.propertyDamageDetails = currentValues.propertyDamageDetails;
    updatedIncident.surfaceType = currentValues.surfaceType;
    updatedIncident.surfaceCondition = currentValues.surfaceCondition;
    updatedIncident.lighting = currentValues.lighting;
    updatedIncident.weatherCondition = currentValues.weatherCondition;

    // Map environmental condition IDs for API compatibility
    updatedIncident.surfaceTypeId = currentValues.surfaceType;
    updatedIncident.surfaceConditionId = currentValues.surfaceCondition;
    updatedIncident.lightingId = currentValues.lighting;
    updatedIncident.weatherConditionId = currentValues.weatherCondition;
    updatedIncident.reportableToAuthorities = currentValues.reportableToAuthorities;
    updatedIncident.reportableDetails = currentValues.reportableDetails;

    // Ensure Actions Taken details are properly saved
    updatedIncident.immediateActionDate = currentValues.immediateActionDate;
    updatedIncident.immediateActionsTaken = currentValues.immediateActionTaken;
    updatedIncident.immediateActionTaken = currentValues.immediateActionTaken;
    updatedIncident.legalClassification = currentValues.immediateActionLegalClassification;
    updatedIncident.immediateActionLegalClassification = currentValues.immediateActionLegalClassification;

    console.log("Saving Actions Taken data:", {
      immediateActionDate: currentValues.immediateActionDate,
      immediateActionTaken: currentValues.immediateActionTaken,
      immediateActionLegalClassification: currentValues.immediateActionLegalClassification
    });

    // Merge photos without losing existing ones - photos are now filenames from API
    updatedIncident.photos = [
      ...(updatedIncident.photos || []),
      ...currentValues.photos
    ];

    // Update uploads field for API submission
    updatedIncident.uploads = currentValues.photos;

    // Merge evidence images without losing existing ones
    updatedIncident.evidence = [
      ...(updatedIncident.evidence || []),
      ...currentValues.evidence
    ];



    // Control Measures fields - ensure we properly merge with existing data
    updatedIncident.incidentGMS = currentValues.incidentGMS;
    updatedIncident.stopWorkOrder = currentValues.stopWorkOrder;
    updatedIncident.rootCauseAnalysis = currentValues.rootCauseAnalysis;
    updatedIncident.controlMeasuresRequired = currentValues.controlMeasuresRequired;
    updatedIncident.controlMeasuresDescription = currentValues.controlMeasuresDescription;
    updatedIncident.controlMeasures = currentValues.controlMeasures;
    updatedIncident.riskAssessmentRequired = currentValues.riskAssessmentRequired;
    updatedIncident.riskAssessmentDescription = currentValues.riskAssessmentDescription;
    updatedIncident.riskAssessments = currentValues.riskAssessments;

    // Investigation fields
    updatedIncident.investigationDetails = currentValues.investigationDetails;
    updatedIncident.locationPlanPhotos = currentValues.locationPlanPhotos;
    updatedIncident.detailedInvestigation = currentValues.detailedInvestigation;
    updatedIncident.comprehensiveInvestigation = currentValues.comprehensiveInvestigation;

    // Calculate and update incident classification
    updatedIncident.classification = calculateIncidentClassification(currentValues.injuryClassification);
    updatedIncident.incidentClassificationLevel = calculateIncidentClassification(currentValues.injuryClassification);

    // Don't change status or workflow stage when just saving
    // Preserve the stage field if it exists
    updatedIncident.stage = updatedIncident.stage || (updatedIncident.status === "under-review" ? "Supplementary information in Progress" : undefined);

    // Set the reviewer name when a reviewer saves changes
    updatedIncident.reviewedBy = userRole === 'reviewer' ? userName : updatedIncident.reviewedBy;

    // Keep the requiresAction flag as true when saving
    updatedIncident.requiresAction = true;
    updatedIncident.lastSavedAt = new Date();
    updatedIncident.lastSavedBy = userName;

    // Original reporter information is already preserved in the spread operator above

    // Ensure we preserve any investigation details that might exist
    if (updatedIncident.investigationDetails) {
      // Keep existing investigation details intact
      updatedIncident.investigationDetails = {
        ...updatedIncident.investigationDetails,
        // Any specific updates to investigation details would go here
      };
    }

    // Update the incident in context
    console.log("🎯 Calling onActionComplete (preview) with applicationDetails:", updatedIncident.applicationDetails);
    onActionComplete(updatedIncident);

    // Show success message
    toast.success("Changes saved successfully", {
      description: "Your changes have been saved. You can continue editing or navigate to other tabs.",
    });
  };

  const onSubmit = async (data: ActionFormValues) => {
    console.log("Action form submitted:", data);
    console.log("Location data being submitted:", data.location);
    console.log("🎯 User role:", userRole);
    console.log("🎯 Incident status:", incident.status);
    console.log("🎯 Workflow stage:", workflowStage);

    if (!incident) {
      toast.error("No incident selected");
      return;
    }



    // Calculate incident classification level based on injury classification using the same logic as new incident form
    let incidentClassificationLevel = calculateIncidentClassification(data.injuryClassification);

    // Determine potential severity level based on answers
    let potentialSeverityLevel = "Not Applicable";

    if (data.potentiallySerious.couldResultInFatality ||
      data.potentiallySerious.couldResultInPermanentDisability) {
      potentialSeverityLevel = "Level 4 - High - Potentially Serious Incident";

      // If the actual incident level is 3 or below, the potential severity takes precedence
      const actualLevel = parseInt(incidentClassificationLevel.split(" ")[1]);
      if (actualLevel <= 3 || isNaN(actualLevel)) {
        incidentClassificationLevel = "Level 4 – High Severity Incident";
      }
    }

    // Log the form data for debugging
    console.log("Form data being submitted:", JSON.stringify(data, null, 2));

    // Log specific sections for debugging
    console.log("Location data being submitted:", data.location);
    console.log("Actions Taken data being submitted:", {
      immediateActionDate: data.immediateActionDate,
      immediateActionTaken: data.immediateActionTaken,
      immediateActionLegalClassification: data.immediateActionLegalClassification
    });

    // Create a deep copy of the incident to ensure we don't lose any data
    const updatedIncident = JSON.parse(JSON.stringify(incident));
    console.log("🎯 ApplicationDetails preserved in updatedIncident:", updatedIncident.applicationDetails);

    // Update the incident with the form data, ensuring we retain all existing data
    // Updated initial details
    updatedIncident.incidentTitle = data.incidentTitle;
    updatedIncident.description = data.description;
    updatedIncident.incidentDate = data.incidentDate;
    updatedIncident.incidentType = data.incidentType;
    updatedIncident.incidentCategory = data.incidentCategory;

    // Map incident category ID to incidentCircumstanceTypeId for API compatibility
    updatedIncident.incidentCircumstanceTypeId = data.incidentCategory;

    // Also update applicationDetails if it exists
    if (updatedIncident.applicationDetails) {
      updatedIncident.applicationDetails.incidentCircumstanceTypeId = data.incidentCategory;
      // Update location IDs in applicationDetails
      updatedIncident.applicationDetails.locationOneId = data.location.country;
      updatedIncident.applicationDetails.locationTwoId = data.location.city;
      updatedIncident.applicationDetails.locationThreeId = data.location.businessUnit;
      updatedIncident.applicationDetails.locationFourId = data.location.projectDcOps;
      updatedIncident.applicationDetails.locationDetails = data.location.levelAndLocation;

      // Also update incidentData within applicationDetails if it exists
      if (updatedIncident.applicationDetails.incidentData) {
        updatedIncident.applicationDetails.incidentData.locationDetails = data.location.levelAndLocation;
      }

      // Update property damage information in applicationDetails
      updatedIncident.applicationDetails.propertyDamage = data.propertyDamage ? "true" : "false";
      updatedIncident.applicationDetails.propertyDamageDetails = data.propertyDamageDetails || "";

      // Update environmental condition IDs in applicationDetails
      updatedIncident.applicationDetails.surfaceTypeId = data.surfaceType;
      updatedIncident.applicationDetails.surfaceConditionId = data.surfaceCondition;
      updatedIncident.applicationDetails.lightingId = data.lighting;
      updatedIncident.applicationDetails.weatherConditionId = data.weatherCondition;

      // Update uploads field in applicationDetails
      updatedIncident.applicationDetails.uploads = data.photos;

      // Update injury classification in applicationDetails
      updatedIncident.applicationDetails.isWorkRelated = data.injuryClassification.isWorkRelated;
      updatedIncident.applicationDetails.lossOfConscious = data.injuryClassification.lossOfConsciousness ? "true" : "false";
      updatedIncident.applicationDetails.dangerousOccurance = data.injuryClassification.isDangerousOccurrence ? "true" : "false";
      updatedIncident.applicationDetails.fatality = data.injuryClassification.isFatality ? "true" : "false";
      updatedIncident.applicationDetails.lostTime = data.injuryClassification.isLostTimeIncident ? "true" : "false";
      updatedIncident.applicationDetails.medicalTreatment = data.injuryClassification.isMedicalTreatment ? "true" : "false";
      updatedIncident.applicationDetails.firstAid = data.injuryClassification.isFirstAid ? "true" : "false";

      // Update reportable to authorities information
      updatedIncident.applicationDetails.reportAuthority = data.reportableToAuthorities ? "true" : "false";
      updatedIncident.applicationDetails.authorityName = data.reportableDetails || "";

      // Update immediate action information
      updatedIncident.applicationDetails.immediateActionTaken = data.immediateActionTaken || "";
      updatedIncident.applicationDetails.immediateActionDate = data.immediateActionDate ? data.immediateActionDate.toISOString() : "";
      updatedIncident.applicationDetails.immediateActionLegalClassification = data.immediateActionLegalClassification || "";

      // Update control measures and risk assessment
      updatedIncident.applicationDetails.isControlMeasure = data.controlMeasuresRequired;
      updatedIncident.applicationDetails.isRiskAssessment = data.riskAssessmentRequired;
      updatedIncident.applicationDetails.stopWorkOrder = data.stopWorkOrder ? "true" : "false";

      // Update classification information
      updatedIncident.applicationDetails.classification = incidentClassificationLevel;
    }

    // Map location data to both old and new field names for compatibility
    updatedIncident.locationCountry = data.location.country;
    updatedIncident.locationCity = data.location.city;
    updatedIncident.locationBusinessUnit = data.location.businessUnit;
    updatedIncident.locationProject = data.location.projectDcOps;
    updatedIncident.locationDetails = data.location.levelAndLocation;

    // Map location IDs for API compatibility
    updatedIncident.locationOneId = data.location.country;
    updatedIncident.locationTwoId = data.location.city;
    updatedIncident.locationThreeId = data.location.businessUnit;
    updatedIncident.locationFourId = data.location.projectDcOps;

    // Keep old field names for backward compatibility
    updatedIncident.country = data.location.country;
    updatedIncident.city = data.location.city;
    updatedIncident.workplaceActivity = data.location.businessUnit;
    updatedIncident.projectDcOps = data.location.projectDcOps;
    updatedIncident.levelAndLocation = data.location.levelAndLocation;

    // Injury Classification - ensure we properly merge with existing data
    updatedIncident.injuryClassification = {
      ...(updatedIncident.injuryClassification || {}),
      isFatality: data.injuryClassification.isFatality,
      isPermanentDisability: data.injuryClassification.isPermanentDisability,
      isLostTimeIncident: data.injuryClassification.isLostTimeIncident,
      isMedicalTreatment: data.injuryClassification.isMedicalTreatment,
      isFirstAid: data.injuryClassification.isFirstAid,
    };

    updatedIncident.incidentClassificationLevel = incidentClassificationLevel;
    updatedIncident.isWorkRelated = data.injuryClassification.isWorkRelated;
    updatedIncident.lossOfConsciousness = data.injuryClassification.lossOfConsciousness;
    updatedIncident.isDangerousOccurrence = data.injuryClassification.isDangerousOccurrence;

    // Classification and additional details
    updatedIncident.potentiallySerious = {
      ...(updatedIncident.potentiallySerious || {}),
      couldResultInFatality: data.potentiallySerious.couldResultInFatality,
      couldResultInPermanentDisability: data.potentiallySerious.couldResultInPermanentDisability,
    };

    updatedIncident.potentialSeverityLevel = potentialSeverityLevel;
    updatedIncident.propertyDamage = data.propertyDamage;
    updatedIncident.propertyDamageDetails = data.propertyDamageDetails;
    updatedIncident.surfaceType = data.surfaceType;
    updatedIncident.surfaceCondition = data.surfaceCondition;
    updatedIncident.lighting = data.lighting;
    updatedIncident.weatherCondition = data.weatherCondition;

    // Map environmental condition IDs for API compatibility
    updatedIncident.surfaceTypeId = data.surfaceType;
    updatedIncident.surfaceConditionId = data.surfaceCondition;
    updatedIncident.lightingId = data.lighting;
    updatedIncident.weatherConditionId = data.weatherCondition;
    updatedIncident.reportableToAuthorities = data.reportableToAuthorities;
    updatedIncident.reportableDetails = data.reportableDetails;
    updatedIncident.immediateActionDate = data.immediateActionDate;
    updatedIncident.immediateActionsTaken = data.immediateActionTaken;
    updatedIncident.immediateActionTaken = data.immediateActionTaken;
    updatedIncident.legalClassification = data.immediateActionLegalClassification;
    updatedIncident.immediateActionLegalClassification = data.immediateActionLegalClassification;

    // Merge photos without losing existing ones - photos are now filenames from API
    updatedIncident.photos = [
      ...(updatedIncident.photos || []),
      ...data.photos
    ];

    // Update uploads field for API submission
    updatedIncident.uploads = data.photos;

    // Merge evidence images without losing existing ones
    updatedIncident.evidence = [
      ...(updatedIncident.evidence || []),
      ...data.evidence
    ];

    // Control Measures fields - ensure we properly merge with existing data
    updatedIncident.incidentGMS = data.incidentGMS;
    updatedIncident.stopWorkOrder = data.stopWorkOrder;
    updatedIncident.rootCauseAnalysis = data.rootCauseAnalysis;
    updatedIncident.controlMeasuresRequired = data.controlMeasuresRequired;
    updatedIncident.controlMeasuresDescription = data.controlMeasuresDescription;
    updatedIncident.controlMeasures = data.controlMeasures;
    updatedIncident.riskAssessmentRequired = data.riskAssessmentRequired;
    updatedIncident.riskAssessmentDescription = data.riskAssessmentDescription;
    updatedIncident.riskAssessments = data.riskAssessments;

    // Investigation fields
    updatedIncident.investigationDetails = data.investigationDetails;
    updatedIncident.locationPlanPhotos = data.locationPlanPhotos;
    updatedIncident.detailedInvestigation = data.detailedInvestigation;
    updatedIncident.comprehensiveInvestigation = data.comprehensiveInvestigation;

    // requiresAction will be set based on the workflow stage and user role below



    // Set the reviewer name when a reviewer takes action
    updatedIncident.reviewedBy = userRole === 'reviewer' ? userName : updatedIncident.reviewedBy;
    updatedIncident.actionTakenAt = new Date();
    updatedIncident.actionTakenBy = userName;

    // Status and stage depend on the user role and current workflow stage
    if (workflowStage === 'comprehensive') {
      // When comprehensive investigation is submitted, close the incident
      updatedIncident.status = "closed";
      updatedIncident.stage = 'Detailed Investigation & Analysis Completed';
      updatedIncident.workflowStage = 'completed';
      updatedIncident.investigationStatus = 'completed';
      updatedIncident.comprehensiveInvestigationStatus = 'completed';
      updatedIncident.closedAt = new Date();
      updatedIncident.closedBy = userName;
      updatedIncident.requiresAction = false; // No further action needed when closed
    } else if (workflowStage === 'investigation' && userRole === 'reviewer') {
      // When reviewer submits investigation, check if comprehensive investigation is needed
      if (data.comprehensiveInvestigation && Object.keys(data.comprehensiveInvestigation).length > 0) {
        // Move to comprehensive investigation stage
        updatedIncident.status = "comprehensive";
        updatedIncident.stage = 'Detailed Investigation & Analysis in Progress';
        updatedIncident.workflowStage = 'comprehensive';
        updatedIncident.investigationStatus = 'completed';
        updatedIncident.comprehensiveInvestigationStatus = 'in-progress';
        updatedIncident.requiresAction = true; // Requires comprehensive investigation
      } else {
        // Close the incident if no comprehensive investigation needed
        updatedIncident.status = "closed";
        updatedIncident.stage = 'Preliminary Analysis Completed';
        updatedIncident.workflowStage = 'completed';
        updatedIncident.investigationStatus = 'completed';
        updatedIncident.closedAt = new Date();
        updatedIncident.closedBy = userName;
        updatedIncident.requiresAction = false; // No further action needed when closed
      }
    } else if (workflowStage === 'investigation' && userRole === 'reporter') {
      // When reporter submits investigation, keep it in investigation stage
      updatedIncident.status = "submitted";
      updatedIncident.stage = 'Preliminary Analysis in Progress';
      updatedIncident.workflowStage = 'investigation';
      updatedIncident.investigationStatus = 'in-progress';
      updatedIncident.requiresAction = true; // Still requires reviewer action
    } else {
      // Original logic for non-investigation submissions
      updatedIncident.status = userRole === 'reviewer' ? "submitted" : "under-review"; // Reviewers set to submitted (Reported), reporters to under-review
      updatedIncident.workflowStage = userRole === 'reviewer' ? 'investigation' : 'review';
      updatedIncident.stage = userRole === 'reviewer' ? 'Preliminary Analysis in Progress' : 'Supplementary information in Progress';
      updatedIncident.investigationStatus = userRole === 'reviewer' ? 'not-started' : updatedIncident.investigationStatus;
      updatedIncident.requiresAction = true; // Requires action for normal workflow
    }

    // Original reporter information is already preserved in the spread operator above
    // No need to reassign these values to themselves

    // Ensure we preserve any investigation details that might exist
    if (updatedIncident.investigationDetails) {
      // Keep existing investigation details intact
      updatedIncident.investigationDetails = {
        ...updatedIncident.investigationDetails,
        // Any specific updates to investigation details would go here
      };
    }

    // Ensure we preserve any comprehensive investigation details that might exist
    if (updatedIncident.comprehensiveInvestigation) {
      // Keep existing comprehensive investigation details intact
      updatedIncident.comprehensiveInvestigation = {
        ...updatedIncident.comprehensiveInvestigation,
        // Any specific updates to comprehensive investigation details would go here
      };
    }

    // Log the updated incident for debugging
    console.log("Updated incident:", JSON.stringify(updatedIncident, null, 2));



    // Check if this is an investigation submission based on workflow stage
    if (workflowStage === 'comprehensive') {
      toast.success("Detailed Investigation & Analysis completed and incident closed!", {
        description: "The detailed investigation & analysis has been submitted and the incident status is now 'Closed' with stage 'Detailed Investigation & Analysis Completed'.",
      });
    } else if (workflowStage === 'investigation' && userRole === 'reviewer') {
      // For reviewer investigation actions, check if this is preliminary analysis stage
      if (incident?.applicationDetails?.stage === 'Preliminary analysis is in progress') {
        try {
          console.log("🔄 Using pre-submit endpoint for preliminary analysis stage");

          // Prepare investigation & analysis data for 'information' key
          const investigationData = {
            investigationDetails: data.investigationDetails || "",
            locationPlanPhotos: data.locationPlanPhotos || [],
            detailedInvestigation: data.detailedInvestigation || {},
            comprehensiveInvestigation: data.comprehensiveInvestigation || {},
          };

          // Prepare control measures data for 'riskcontrol' key
          const controlMeasuresData = {
            controlMeasuresRequired: data.controlMeasuresRequired,
            controlMeasuresDescription: data.controlMeasuresDescription || "",
            controlMeasures: data.controlMeasures || [],
            riskAssessmentRequired: data.riskAssessmentRequired,
            riskAssessmentDescription: data.riskAssessmentDescription || "",
            riskAssessments: data.riskAssessments || [],
            stopWorkOrder: data.stopWorkOrder,
            incidentGMS: data.incidentGMS || "",
            rootCauseAnalysis: data.rootCauseAnalysis || "",
          };

          // Prepare the pre-submit data with only investigation and control measures
          const preSubmitData = {
            informationStep: investigationData,
            riskControl: controlMeasuresData,
          };

          console.log("📤 Sending pre-submit request with investigation and control measures only:", preSubmitData);

          // Call the pre-submit API with only investigation and control measures data
          await ApiService.preSubmitIncidentReview(incident.applicationDetails.id, preSubmitData);

          console.log("✅ Pre-submit request successful (preliminary analysis stage)");

        } catch (error) {
          console.error("❌ Pre-submit request failed (preliminary analysis stage):", error);
          toast.error("Failed to submit investigation details. Please try again.");
          return; // Exit early if API call fails
        }
      } else {
        // For other investigation stages, use the regular PATCH API
        const shouldUsePatchForInvestigation = incident.status === 'Reported' || incident.status === 'reported' || incident.status === 'submitted';

        if (shouldUsePatchForInvestigation) {
          try {
            console.log("🔄 Using PATCH endpoint for reviewer investigation action");

            // Consolidate ONLY ACCEPTED fields into applicationDetails for investigation
            // First, filter out rejected fields from existing applicationDetails
            const existingDetails = updatedIncident.applicationDetails || incident.applicationDetails || {};
            const {
              locationOne, locationTwo, locationThree, locationFour,
              submittedUser, assignedTo, locationDetails, time,
              permanentDisability, controlMeasuresDescription, riskAssessmentDescription,
              potentialSeverityLevel, workflowStage, stage, reviewedBy,
              actionTakenAt, actionTakenBy, investigationDetails, rootCauseAnalysis,
              couldResultInFatality, couldResultInPermanentDisability,
              ...filteredExistingDetails
            } = existingDetails;

            const consolidatedApplicationDetails = {
              // Preserve only accepted fields from existing applicationDetails
              ...filteredExistingDetails,

              // Basic incident information
              title: data.incidentTitle,
              description: data.description,
              date: data.incidentDate ? data.incidentDate.toISOString() : new Date().toISOString(),

              // Location hierarchy (only IDs)
              locationOneId: data.location.country,
              locationTwoId: data.location.city,
              locationThreeId: data.location.businessUnit,
              locationFourId: data.location.projectDcOps,
              locationFiveId: data.location.levelAndLocation,
              locationSixId: data.location.levelAndLocation, // If needed

              // Incident classification
              incidentCircumstanceCategoryId: data.incidentType,
              incidentCircumstanceTypeId: data.incidentCategory,

              // Injury classification
              isWorkRelated: data.injuryClassification.isWorkRelated,
              lossOfConscious: data.injuryClassification.lossOfConsciousness ? "true" : "false",
              dangerousOccurance: data.injuryClassification.isDangerousOccurrence ? "true" : "false",
              fatality: data.injuryClassification.isFatality ? "true" : "false",
              lostTime: data.injuryClassification.isLostTimeIncident ? "true" : "false",
              medicalTreatment: data.injuryClassification.isMedicalTreatment ? "true" : "false",
              firstAid: data.injuryClassification.isFirstAid ? "true" : "false",

              // Property damage
              propertyDamage: data.propertyDamage ? "true" : "false",
              propertyDamageDetails: data.propertyDamageDetails || "",

              // Environmental conditions
              surfaceTypeId: data.surfaceType,
              surfaceConditionId: data.surfaceCondition,
              lightingId: data.lighting,
              weatherConditionId: data.weatherCondition,

              // Photo uploads from /files API
              uploads: data.photos,
              evidence: data.evidence,

              // Reportable to authorities
              reportAuthority: data.reportableToAuthorities ? "true" : "false",
              authorityName: data.reportableDetails || "",

              // Immediate actions
              immediateActionTaken: data.immediateActionTaken || "",
              immediateActionDate: data.immediateActionDate ? data.immediateActionDate.toISOString() : "",
              immediateActionLegalClassification: data.immediateActionLegalClassification || "",

              // Control measures and risk assessment
              isControlMeasure: data.controlMeasuresRequired,
              isRiskAssessment: data.riskAssessmentRequired,
              stopWorkOrder: data.stopWorkOrder ? "true" : "false",

              // Classification
              classification: incidentClassificationLevel,

              // Investigation analysis data
              investigationDetails: data.investigationDetails || "",
              locationPlanPhotos: data.locationPlanPhotos || [],
              detailedInvestigation: data.detailedInvestigation || {},
              comprehensiveInvestigation: data.comprehensiveInvestigation || {},
            };

            console.log("📤 Sending PATCH request (investigation) with consolidated applicationDetails:", consolidatedApplicationDetails);

            // Call the PATCH API with only applicationDetails
            await ApiService.updateIncidentReview(incident.id, { applicationDetails: consolidatedApplicationDetails });

            console.log("✅ PATCH request successful (reviewer investigation)");

          } catch (error) {
            console.error("❌ PATCH request failed (reviewer investigation):", error);
            toast.error("Failed to submit incident review. Please try again.");
            return; // Exit early if API call fails
          }
        }
      }

      // Check if comprehensive investigation is needed
      if (data.comprehensiveInvestigation && Object.keys(data.comprehensiveInvestigation).length > 0) {
        toast.success("Investigation completed - Moving to Detailed Investigation & Analysis!", {
          description: "The preliminary investigation has been completed. The incident is now in 'Detailed Investigation & Analysis in Progress' stage.",
        });
      } else {
        toast.success("Investigation completed and incident closed!", {
          description: "The investigation has been submitted and the incident status is now 'Closed' with stage 'Preliminary Analysis Completed'.",
        });
      }
    } else if (workflowStage === 'investigation' && userRole === 'reporter') {
      toast.success("Investigation submitted successfully!", {
        description: "Your investigation report has been submitted and the incident has been updated.",
      });
    } else if (userRole === 'reviewer') {
      // For reviewer actions, use the PATCH API if incident status is "Reported"
      // const shouldUsePatchForReviewer = incident.status === 'Reported' || incident.status === 'reported' || incident.status === 'submitted';

      // if (shouldUsePatchForReviewer) {
      try {
        console.log("🔄 Using PATCH endpoint for reviewer action (general case)");

        // Consolidate ONLY ACCEPTED fields into applicationDetails
        // First, filter out rejected fields from existing applicationDetails
        const existingDetails = updatedIncident.applicationDetails || incident.applicationDetails || {};
        const {
          locationOne, locationTwo, locationThree, locationFour,
          submittedUser, assignedTo, locationDetails, time,
          permanentDisability, controlMeasuresDescription, riskAssessmentDescription,
          potentialSeverityLevel, workflowStage, stage, reviewedBy,
          actionTakenAt, actionTakenBy, investigationDetails, rootCauseAnalysis,
          couldResultInFatality, couldResultInPermanentDisability,
          ...filteredExistingDetails
        } = existingDetails;

        const consolidatedApplicationDetails = {
          // Preserve only accepted fields from existing applicationDetails
          ...filteredExistingDetails,

          // Basic incident information
          title: data.incidentTitle,
          description: data.description,
          date: data.incidentDate ? data.incidentDate.toISOString() : new Date().toISOString(),

          // Location hierarchy (only IDs)
          locationOneId: data.location.country,
          locationTwoId: data.location.city,
          locationThreeId: data.location.businessUnit,
          locationFourId: data.location.projectDcOps,
          locationFiveId: data.location.levelAndLocation,
          locationSixId: data.location.levelAndLocation, // If needed

          // Incident classification
          incidentCircumstanceCategoryId: data.incidentType,
          incidentCircumstanceTypeId: data.incidentCategory,

          // Injury classification
          isWorkRelated: data.injuryClassification.isWorkRelated,
          lossOfConscious: data.injuryClassification.lossOfConsciousness ? "true" : "false",
          dangerousOccurance: data.injuryClassification.isDangerousOccurrence ? "true" : "false",
          fatality: data.injuryClassification.isFatality ? "true" : "false",
          lostTime: data.injuryClassification.isLostTimeIncident ? "true" : "false",
          medicalTreatment: data.injuryClassification.isMedicalTreatment ? "true" : "false",
          firstAid: data.injuryClassification.isFirstAid ? "true" : "false",

          // Property damage
          propertyDamage: data.propertyDamage ? "true" : "false",
          propertyDamageDetails: data.propertyDamageDetails || "",

          // Environmental conditions
          surfaceTypeId: data.surfaceType,
          surfaceConditionId: data.surfaceCondition,
          lightingId: data.lighting,
          weatherConditionId: data.weatherCondition,

          // Photo uploads from /files API
          uploads: data.photos,
          evidence: data.evidence,

          // Reportable to authorities
          reportAuthority: data.reportableToAuthorities ? "true" : "false",
          authorityName: data.reportableDetails || "",

          // Immediate actions
          immediateActionTaken: data.immediateActionTaken || "",
          immediateActionDate: data.immediateActionDate ? data.immediateActionDate.toISOString() : "",
          immediateActionLegalClassification: data.immediateActionLegalClassification || "",

          // Control measures and risk assessment
          isControlMeasure: data.controlMeasuresRequired,
          isRiskAssessment: data.riskAssessmentRequired,
          stopWorkOrder: data.stopWorkOrder ? "true" : "false",

          // Classification
          classification: incidentClassificationLevel,

          // Investigation analysis data
          // investigationDetails: data.investigationDetails || "",
          // locationPlanPhotos: data.locationPlanPhotos || [],
          // detailedInvestigation: data.detailedInvestigation || {},
          // comprehensiveInvestigation: data.comprehensiveInvestigation || {},
        };

        console.log("📤 Sending PATCH request with consolidated applicationDetails:", consolidatedApplicationDetails);

        // Call the PATCH API with only applicationDetails
        await ApiService.updateIncidentReview(incident.applicationDetails.id, consolidatedApplicationDetails);

        console.log("✅ PATCH request successful (reviewer general case)");

      } catch (error) {
        console.error("❌ PATCH request failed (reviewer general case):", error);
        toast.error("Failed to submit incident review. Please try again.");
        return; // Exit early if API call fails
      }
      // }

      toast.success("Incident submitted successfully!", {
        description: "The incident is now in the 'Preliminary Analysis in Progress' stage. Both Reporter and Reviewer can now update Investigation Details in their 'My Actions' tab.",
      });
    } else {
      toast.success("Incident updated successfully!", {
        description: "Your changes have been saved and will be visible to the reviewer.",
      });
    }

    console.log("🎯 Calling onActionComplete with applicationDetails:", updatedIncident.applicationDetails);

    // Use the original flow - API calls are now handled in the specific reviewer conditions above
    onActionComplete(updatedIncident);
    onOpenChange(false);
  };



  const legalClassifications = [
    { value: "normal", label: "Normal" },
    { value: "client-privilege", label: "Client Privilege" },
  ];



  // Custom styles for form elements to match the Report an Incident form
  const formLabelClass = "text-base font-medium mb-2";
  const formInputClass = "w-full";
  const formSectionTitleClass = "flex items-center gap-3 mb-6";
  const formSectionIconClass = "flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] overflow-y-auto p-0">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold">
            Take Action on Incident: {incident?.applicationDetails?.maskId || ''}
          </DialogTitle>
          <DialogDescription>
            Please review and update the incident information as needed.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 pb-6">
          {/* Workflow Step Indicator */}
          <div className={cn(
            "mb-6 rounded-lg",
            workflowStage !== 'initial' ? "bg-green-50 border border-green-200 p-4" : ""
          )}>
            <WorkflowStepIndicator
              currentStage={workflowStage as 'initial' | 'Reported' | 'investigation' | 'comprehensive'}
              userRole={userRole}
              isInitialReportCompleted={isInitialReportCompleted}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* Show different tabs based on the workflow stage */}
            {workflowStage === 'comprehensive' ? (
              // For Detailed Investigation & Analysis phase (Step 4), show only comprehensive investigation tab
              <TabsList className="grid grid-cols-1 mb-6">
                <TabsTrigger value="comprehensive-investigation">Detailed Investigation & Analysis</TabsTrigger>
              </TabsList>
            ) : workflowStage === 'investigation' ? (
              // For Investigation phase (Step 3), show investigation-related tabs for both Reporter and Reviewer
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="investigation">Investigation & Analysis</TabsTrigger>
                <TabsTrigger value="control-measures">Control Measures</TabsTrigger>
              </TabsList>
            ) : (
              // For other phases, show the standard tabs
              <TabsList className={`grid ${userRole === 'reviewer' ? 'grid-cols-6' : 'grid-cols-5'} mb-6`}>
                <TabsTrigger value="details">Incident Details</TabsTrigger>
                <TabsTrigger value="classification">Classification</TabsTrigger>
                <TabsTrigger value="conditions">Conditions</TabsTrigger>
                <TabsTrigger value="actions">Immediate Actions Taken</TabsTrigger>
                <TabsTrigger value="attachments">Attachments</TabsTrigger>
                {userRole === 'reviewer' && (
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                )}
              </TabsList>
            )}

            <Form {...form}>
              <form onSubmit={(e) => {
                // Only allow form submission from the Complete Action button
                // This prevents accidental submission when pressing Enter
                e.preventDefault();
              }} className="space-y-6">
                <TabsContent value="details" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Info size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Incident Information Required in Initial Reporting</h2>
                    </div>

                    <div className="space-y-6">
                      {/* Incident Title Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-purple-100 text-purple-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-heading-1"><path d="M4 12h8" /><path d="M4 18V6" /><path d="M12 18V6" /><path d="m17 12 3-2v8" /></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Incident Title</h3>
                        </div>

                        {/* Incident Title */}
                        <FormField
                          control={form.control}
                          name="incidentTitle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={formLabelClass}>1. Incident Title</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter a descriptive title for the incident"
                                  {...field}
                                  className={`${formInputClass} h-12 border-2 bg-white`}
                                />
                              </FormControl>
                              <FormDescription>
                                Free writing field - provide a clear, concise title
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Date and Time Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar-clock"><path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.5" /><path d="M16 2v4" /><path d="M8 2v4" /><path d="M3 10h18" /><circle cx="17" cy="16" r="3" /><path d="M17 14.5v1.5h1.5" /></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Date and Time</h3>
                        </div>

                        <div className="grid md:grid-cols-1 gap-x-6 gap-y-4">
                          {/* Incident Date and Time */}
                          <FormField
                            control={form.control}
                            name="incidentDate"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>2. Incident Date & Time</FormLabel>
                                <FormControl>
                                  <input
                                    type="datetime-local"
                                    value={field.value ? format(field.value, "yyyy-MM-dd'T'HH:mm") : ""}
                                    onChange={(e) => {
                                      const dateValue = e.target.value ? new Date(e.target.value) : null;
                                      field.onChange(dateValue);
                                    }}
                                    max={format(new Date(), "yyyy-MM-dd'T'HH:mm")}
                                    className={`${formInputClass} h-12 border-2 bg-white w-full px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Select the date and time when the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Incident Type Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-alert-triangle"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" /><path d="M12 9v4" /><path d="M12 17h.01" /></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Incident Type</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                          <FormField
                            control={form.control}
                            name="incidentType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>
                                  3. Incident Type

                                </FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  disabled={loadingCategories}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder={loadingCategories ? "Loading incident types..." : "Select incident type"} />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {loadingCategories ? (
                                      <SelectItem value="loading" disabled>Loading...</SelectItem>
                                    ) : (
                                      incidentCategories
                                        .filter((category) => category.name !== "Environmental")
                                        .map((category) => (
                                          <SelectItem key={category.id} value={category.id}>
                                            {category.name}
                                          </SelectItem>
                                        ))
                                    )}
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select one - Health or Safety
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="incidentCategory"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>
                                  4. Incident Category

                                </FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  disabled={loadingTypes}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder={loadingTypes ? "Loading incident categories..." : "Select incident category"} />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {loadingTypes ? (
                                      <SelectItem value="loading" disabled>Loading...</SelectItem>
                                    ) : (
                                      incidentTypes.map((type) => (
                                        <SelectItem key={type.id} value={type.id}>
                                          {type.name}
                                        </SelectItem>
                                      ))
                                    )}
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the specific category of the incident
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Description Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" /><polyline points="14 2 14 8 20 8" /><line x1="16" x2="8" y1="13" y2="13" /><line x1="16" x2="8" y1="17" y2="17" /><line x1="10" x2="8" y1="9" y2="9" /></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Description</h3>
                        </div>

                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={formLabelClass}>5. Brief Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  className="min-h-24 border-2 bg-white"
                                  placeholder="Provide a detailed description of the incident..."
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Provide a clear description of what happened, including relevant details
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Location Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-indigo-100 text-indigo-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" /><circle cx="12" cy="10" r="3" /></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Geographic Location</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                          {/* Location One */}
                          <FormField
                            control={form.control}
                            name="location.country"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>6. {getDynamicTitle('LocationOne')}</FormLabel>
                                <Select
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                    // Reset dependent locations
                                    form.setValue("location.city", "");
                                    form.setValue("location.businessUnit", "");
                                    form.setValue("location.projectDcOps", "");
                                    // Load next level
                                    if (value) {
                                      loadLocationTwos(value);
                                    }
                                  }}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select location" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {locationOnes.map((location) => (
                                      <SelectItem key={location.id} value={location.id}>
                                        {location.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Location Two */}
                          <FormField
                            control={form.control}
                            name="location.city"
                            render={({ field }) => {
                              const selectedLocationOne = form.watch("location.country");

                              return (
                                <FormItem>
                                  <FormLabel className={formLabelClass}>{getDynamicTitle('LocationTwo')}</FormLabel>
                                  <Select
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                      // Reset dependent locations
                                      form.setValue("location.businessUnit", "");
                                      form.setValue("location.projectDcOps", "");
                                      // Load next level
                                      if (value) {
                                        loadLocationThrees(value);
                                      }
                                    }}
                                    value={field.value}
                                    disabled={!selectedLocationOne}
                                  >
                                    <FormControl>
                                      <SelectTrigger className="h-12 border-2 bg-white">
                                        <SelectValue placeholder="Select location" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {locationTwos.map((location) => (
                                        <SelectItem key={location.id} value={location.id}>
                                          {location.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              );
                            }}
                          />
                        </div>

                        <div className="mt-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-building"><rect width="16" height="20" x="4" y="2" rx="2" ry="2" /><path d="M9 22v-4h6v4" /><path d="M8 6h.01" /><path d="M16 6h.01" /><path d="M12 6h.01" /><path d="M12 10h.01" /><path d="M12 14h.01" /><path d="M16 10h.01" /><path d="M16 14h.01" /><path d="M8 10h.01" /><path d="M8 14h.01" /></svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-700">Business Information</h3>
                          </div>

                          <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                            {/* Location Three */}
                            <FormField
                              control={form.control}
                              name="location.businessUnit"
                              render={({ field }) => {
                                const selectedLocationTwo = form.watch("location.city");

                                return (
                                  <FormItem>
                                    <FormLabel className={formLabelClass}>{getDynamicTitle('LocationThree')}</FormLabel>
                                    <Select
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // Reset dependent location
                                        form.setValue("location.projectDcOps", "");
                                        // Load next level
                                        if (value) {
                                          loadLocationFours(value);
                                        }
                                      }}
                                      value={field.value}
                                      disabled={!selectedLocationTwo}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="h-12 border-2 bg-white">
                                          <SelectValue placeholder="Select location" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {locationThrees.map((location) => (
                                          <SelectItem key={location.id} value={location.id}>
                                            {location.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                );
                              }}
                            />

                            {/* Location Four */}
                            <FormField
                              control={form.control}
                              name="location.projectDcOps"
                              render={({ field }) => {
                                const selectedLocationThree = form.watch("location.businessUnit");

                                return (
                                  <FormItem>
                                    <FormLabel className={formLabelClass}>{getDynamicTitle('LocationFour')}</FormLabel>
                                    <Select
                                      onValueChange={field.onChange}
                                      value={field.value}
                                      disabled={!selectedLocationThree}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="h-12 border-2 bg-white">
                                          <SelectValue placeholder="Select location" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {locationFours.map((location) => (
                                          <SelectItem key={location.id} value={location.id}>
                                            {location.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                );
                              }}
                            />
                          </div>
                        </div>

                        {/* Specific Location Details */}
                        <div className="mt-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-locate-fixed"><line x1="2" x2="22" y1="12" y2="12" /><line x1="12" x2="12" y1="2" y2="22" /><circle cx="12" cy="12" r="7" /><circle cx="12" cy="12" r="3" /></svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-700">Specific Location</h3>
                          </div>

                          <FormField
                            control={form.control}
                            name="location.levelAndLocation"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>{getDynamicTitle('LocationFive')} and {getDynamicTitle('LocationSix')} Details</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter specific level and location details (e.g., Building 3, Floor 2, Room 201)"
                                    {...field}
                                    className={`h-12 border-2 bg-white ${formInputClass}`}
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Provide specific details about the exact location where the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="classification" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <AlertTriangle size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Injury Classification</h2>
                    </div>

                    <div className="space-y-6 border p-4 rounded-md">
                      <p className="text-sm text-muted-foreground mb-4">
                        Update the injury classification data as needed based on your assessment.
                      </p>

                      {/* Work Related Question */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isWorkRelated"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">1. Is this incident work related?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Work-related incidents occur during work activities or as a result of performing work duties. Only work-related incidents are counted in official incident statistics.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Loss of Consciousness */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.lossOfConsciousness"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">2. Was there a loss of consciousness?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Loss of consciousness refers to any period where the injured person was unresponsive, regardless of duration. This is a serious medical condition that requires immediate attention.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Dangerous Occurrence */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isDangerousOccurrence"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">3. Was this as a result of Dangerous Occurrence?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Dangerous Occurrence is an unplanned, uncontrolled event that had the potential to cause injury, ill health or damage, but did not actually do so. Examples include structural collapses, explosions, or equipment failures.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-2 pb-1">
                        <h3 className="text-lg font-medium">Did this incident result in:</h3>
                      </div>

                      {/* Fatality */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFatality"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">A fatality?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A fatality is a death resulting from a work-related incident or occupational illness. This is classified as a Level 5 Critical Incident and requires immediate reporting to authorities.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Permanent Disability */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isPermanentDisability"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">An injury or occupational illness resulting in permanent disability?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Permanent disability refers to any injury or illness that results in permanent impairment of bodily functions, including loss of limbs, paralysis, or permanent damage to organs or senses. This is classified as a Level 4 High Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Lost Time Incident */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isLostTimeIncident"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Lost Time Incident (LTI)?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Lost Time Incident (LTI) is an injury or illness that results in the employee being unable to work for one or more scheduled workdays after the day of the incident. This is classified as a Level 3 Medium Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Medical Treatment */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isMedicalTreatment"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Medical Treatment of Illness/Injury?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Medical Treatment Injury (MTI) refers to injuries that require treatment beyond first aid, administered by a physician or other medical professional. This is classified as a Level 2 Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* First Aid */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFirstAid"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Need to administer First Aid?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>First Aid Injury (FAI) refers to minor injuries that can be treated on-site using basic first aid supplies and do not require professional medical care. This is classified as a Level 1 Very Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="text-sm text-muted-foreground italic mt-4 p-3 bg-gray-50 rounded-md">
                        <p>If all the answers above are "No", then the incident is classified as a "NEAR MISS".</p>
                      </div>

                      {/* Incident Classification Result */}
                      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <h3 className="font-bold text-lg flex items-center">
                          <AlertTriangle className="mr-2 h-5 w-5 text-yellow-600" />
                          Incident Classification Result
                        </h3>
                        <p className="mt-2 font-medium">
                          {form.getValues("injuryClassification.isFatality") === true && "Level 5 – Critical Incident"}
                          {form.getValues("injuryClassification.isPermanentDisability") === true && form.getValues("injuryClassification.isFatality") !== true && "Level 4 – High Severity Incident"}
                          {form.getValues("injuryClassification.isLostTimeIncident") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && "Level 3 – LTI - Medium Severity Incident "}
                          {form.getValues("injuryClassification.isMedicalTreatment") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && "Level 2 – Low Severity Incident (MTI)"}
                          {form.getValues("injuryClassification.isFirstAid") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && form.getValues("injuryClassification.isMedicalTreatment") !== true && "Level 1 – Very Low Severity Incident (FAI)"}
                          {form.getValues("injuryClassification.isFatality") === false && form.getValues("injuryClassification.isPermanentDisability") === false && form.getValues("injuryClassification.isLostTimeIncident") === false && form.getValues("injuryClassification.isMedicalTreatment") === false && form.getValues("injuryClassification.isFirstAid") === false && "NEAR MISS"}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <FormField
                        control={form.control}
                        name="propertyDamage"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Property Damage</FormLabel>
                              <FormDescription>
                                Check if this incident resulted in property damage
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {watchPropertyDamage && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name="propertyDamageDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Property Damage Details</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Describe the property damage..."
                                  className="min-h-24"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="conditions" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Info size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Environmental Conditions</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="surfaceType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Surface Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select surface type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {surfaceTypes.map((type) => (
                                  <SelectItem key={type.id} value={type.id}>
                                    {type.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="surfaceCondition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Surface Condition</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select surface condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {surfaceConditions.map((condition) => (
                                  <SelectItem key={condition.id} value={condition.id}>
                                    {condition.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="lighting"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Lighting</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select lighting condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {lightings.map((lighting) => (
                                  <SelectItem key={lighting.id} value={lighting.id}>
                                    {lighting.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="weatherCondition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Weather Condition</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select weather condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {weatherConditions.map((condition) => (
                                  <SelectItem key={condition.id} value={condition.id}>
                                    {condition.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="mt-6">
                      <FormField
                        control={form.control}
                        name="reportableToAuthorities"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Is this incident reportable to local authority and/or customer?</FormLabel>
                              <FormDescription>
                                Check if this incident needs to be reported to regulatory bodies, police, etc.
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {watchReportableToAuthorities && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name="reportableDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Reporting Details</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Provide details about which authorities need to be notified and why..."
                                  className="min-h-24"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="actions" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Check size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Immediate Actions Taken</h2>
                    </div>

                    <div className="space-y-6">
                      <FormField
                        control={form.control}
                        name="immediateActionDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className={formLabelClass}>Action Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>Select date</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="immediateActionTaken"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Actions Taken</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe the immediate actions taken to address the incident..."
                                className="min-h-32"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="immediateActionLegalClassification"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Legal Classification</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select legal classification" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {legalClassifications.map((classification) => (
                                  <SelectItem key={classification.value} value={classification.value}>
                                    {classification.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />


                    </div>
                  </div>
                </TabsContent>



                <TabsContent value="attachments" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Upload size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Upload Photos/Files</h2>
                    </div>

                    <p className="mb-4 text-gray-600">
                      Upload photos of the incident to provide visual evidence. Photos significantly help in
                      investigating and addressing the incident properly.
                    </p>

                    <FormField
                      control={form.control}
                      name="photos"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-medium">General Photos</FormLabel>
                          <FormControl>
                            <PhotoUpload
                              onPhotosChange={(uploads) => field.onChange(uploads)}
                              existingPhotos={field.value as string[]}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Evidence Images Section */}
                    {/* <div className="mt-8 pt-6 border-t border-gray-200">
                      <h3 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                        <FileText size={18} className="mr-2 text-red-600" />
                        Evidence Images
                      </h3>
                      <p className="mb-4 text-gray-600">
                        Upload specific evidence images that directly relate to the incident investigation.
                        These images will be used as formal evidence in the incident analysis.
                      </p>

                      <FormField
                        control={form.control}
                        name="evidence"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium">Evidence Photos</FormLabel>
                            <FormControl>
                              <PhotoUpload
                                onPhotosChange={(uploads) => field.onChange(uploads)}
                                existingPhotos={field.value as string[]}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div> */}
                  </div>
                </TabsContent>

                {/* Investigation tabs - shown when workflowStage is 'investigation' */}
                {workflowStage === 'investigation' && (
                  <>
                    <TabsContent value="investigation" className="space-y-6">
                      {/* Basic Investigation Section */}
                      <div className="form-section">
                        <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <FileText size={20} />
                          </div>
                          <h2 className="text-xl font-semibold">Preliminary Investigation & Analysis </h2>
                        </div>

                        <div className="space-y-6">
                          <FormField
                            control={form.control}
                            name="investigationDetails"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>Investigation Details</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter detailed investigation findings..."
                                    className="min-h-[150px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Provide a comprehensive description of your investigation findings.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="rootCauseAnalysis"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>Root Cause Analysis</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter root cause analysis..."
                                    className="min-h-[150px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Identify and document the root causes that contributed to this incident.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Detailed Investigation Section */}
                      <div className="form-section">
                        {/* <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <Microscope size={20} />
                          </div>
                          <h2 className="text-xl font-semibold"> Investigation and Analysis</h2>
                        </div> */}

                        {/* <div className="p-4 border rounded-md bg-blue-50 mb-4">
                          <div className="flex items-start gap-2">
                            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-blue-800">Complete Investigation Details</p>
                              <p className="text-sm text-blue-700 mt-1">
                                Please complete all sections of the detailed investigation form below. This information is critical for understanding the incident and preventing future occurrences.
                              </p>
                            </div>
                          </div>
                        </div> */}

                        {/* Use the InvestigationAnalysisForm component with isNested=true */}
                        <InvestigationAnalysisForm
                          incident={incident}
                          isNested={true}
                          onSubmit={(data) => {
                            // Update the form with the data from the InvestigationAnalysisForm
                            console.log("IncidentActionDialog - onSubmit callback called with data:", data);
                            form.setValue('detailedInvestigation', data, { shouldValidate: true });
                            console.log("IncidentActionDialog - After setting form value, form values:", form.getValues());
                          }}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="control-measures" className="space-y-6">
                      <div className="form-section">
                        <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <FileText size={20} />
                          </div>
                          <h2 className="text-xl font-semibold">Control Measures</h2>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              console.log("🧪 Manual reload: Loading location users");
                              const appDetails = incident?.applicationDetails || {};
                              const locationParams = {
                                locationOneId: appDetails.locationOneId,
                                locationTwoId: appDetails.locationTwoId,
                                locationThreeId: appDetails.locationThreeId,
                                locationFourId: appDetails.locationFourId
                              };
                              console.log("🧪 Manual reload: Location params:", locationParams);
                              loadLocationUsers(locationParams);
                            }}
                            className="ml-auto"
                          >
                            🔄 Reload Users ({locationUsers.length})
                          </Button>
                        </div>

                        <div className="space-y-6 mb-8">
                          {/* Immediate Actions Taken Section */}
                          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                            <div className="flex items-center gap-2 mb-4">
                              <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-shield-check"><path d="M20 13c0 5-3.5 7.5-8 7.5s-8-2.5-8-7.5c0-1.3.3-2.5.8-3.7L12 3l7.2 6.3c.5 1.2.8 2.4.8 3.7z" /><path d="m9 12 2 2 4-4" /></svg>
                              </div>
                              <h3 className="text-sm font-medium text-gray-700">Immediate Actions Taken (Post Damage / Incident) *</h3>
                            </div>

                            <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                              {/* Action Date */}
                              <FormField
                                control={form.control}
                                name="immediateActionDate"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-base font-medium">Action Date *</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="date"
                                        {...field}
                                        value={field.value ? format(field.value, "yyyy-MM-dd") : ""}
                                        onChange={(e) => {
                                          const dateValue = e.target.value ? new Date(e.target.value) : new Date();
                                          field.onChange(dateValue);
                                        }}
                                        className="h-12 border-2 bg-white"
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      Select the date when immediate actions were taken
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              {/* Placeholder for alignment */}
                              <div></div>
                            </div>

                            {/* Actions Description */}
                            <FormField
                              control={form.control}
                              name="immediateActionTaken"
                              render={({ field }) => (
                                <FormItem className="mt-4">
                                  <FormLabel className="text-base font-medium">
                                    Description of Immediate Actions Taken *
                                  </FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Describe the immediate actions taken after the incident occurred..."
                                      className="min-h-[120px] border-2 bg-white"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Provide details of any immediate actions taken to address the incident, secure the area, provide first aid, etc.
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={form.control}
                            name="controlMeasuresRequired"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4 bg-slate-50">
                                <div className="space-y-1 flex-1">
                                  <FormLabel className="text-base">
                                    Are control measures required?
                                  </FormLabel>
                                  <FormDescription>
                                    Toggle this switch if additional control measures are needed to prevent recurrence.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {form.watch("controlMeasuresRequired") && (
                            <div className="border rounded-md p-4 bg-slate-50">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="font-medium">Control Measures to be Implemented</h3>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const currentMeasures = watchControlMeasures || [];
                                    form.setValue("controlMeasures", [...currentMeasures, { controlMeasures: "", completionDate: undefined, personResponsible: "" }], { shouldDirty: true });
                                  }}
                                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                                >
                                  Add Measure
                                </Button>
                              </div>

                              {watchControlMeasures?.length === 0 && (
                                <div className="text-center py-4 text-slate-500">
                                  No control measures added. Click "Add Measure" to add one.
                                </div>
                              )}

                              <div className="grid grid-cols-3 gap-4 mb-2 px-3">
                                <div className="font-medium text-sm text-gray-600">Corrective/Control measures:</div>
                                <div className="font-medium text-sm text-gray-600">Due Date</div>
                                <div className="font-medium text-sm text-gray-600">Person Responsible</div>
                              </div>

                              {watchControlMeasures?.map((_, index) => (
                                <div key={index} className="grid grid-cols-3 gap-4 items-center mb-3 pb-3 border-b last:border-0 bg-white p-3 rounded-md shadow-sm">
                                  <FormField
                                    control={form.control}
                                    name={`controlMeasures.${index}.controlMeasures`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <FormControl>
                                          <Input
                                            placeholder="Enter control measure..."
                                            {...field}
                                            className="h-10"
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`controlMeasures.${index}.completionDate`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant={"outline"}
                                                className={cn(
                                                  "w-full pl-3 text-left font-normal h-10",
                                                  !field.value && "text-muted-foreground"
                                                )}
                                              >
                                                {field.value ? (
                                                  format(field.value, "dd-MM-yyyy")
                                                ) : (
                                                  <span>Select date</span>
                                                )}
                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                              mode="single"
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              initialFocus
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <div className="flex items-center gap-2">
                                    <FormField
                                      control={form.control}
                                      name={`controlMeasures.${index}.personResponsible`}
                                      render={({ field }) => (
                                        <FormItem className="flex-1 mb-0">
                                          <Select onValueChange={field.onChange} value={field.value || ""}>
                                            <FormControl>
                                              <SelectTrigger className="h-10">
                                                <SelectValue placeholder="Choose" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              {loadingUsers ? (
                                                <div className="p-2 text-sm text-gray-500">Loading users...</div>
                                              ) : locationUsers.length > 0 ? (
                                                locationUsers.map((user) => (
                                                  <SelectItem key={user.value} value={user.value}>
                                                    {user.label}
                                                  </SelectItem>
                                                ))
                                              ) : (
                                                <div className="p-2 text-sm text-gray-500">No users found</div>
                                              )}
                                            </SelectContent>
                                          </Select>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-10 w-10 rounded-full text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                      onClick={() => {
                                        const currentMeasures = watchControlMeasures || [];
                                        const updatedMeasures = currentMeasures.filter((_, i) => i !== index);
                                        form.setValue("controlMeasures", updatedMeasures, { shouldDirty: true });
                                      }}
                                    >
                                      <X className="h-5 w-5" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Risk Assessment Section */}
                        <div className="space-y-6 mb-8">
                          <FormField
                            control={form.control}
                            name="riskAssessmentRequired"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4 bg-slate-50">
                                <div className="space-y-1 flex-1">
                                  <FormLabel className="text-base">
                                    Is risk assessments and safe working procedures need to be reviewed and updated?
                                  </FormLabel>
                                  <FormDescription>
                                    Toggle this switch if risk assessments and safe working procedures need to be reviewed and updated.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {form.watch("riskAssessmentRequired") && (
                            <div className="border rounded-md p-4 bg-slate-50">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="font-medium">Risk Assessments to be Reviewed/Updated</h3>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const currentAssessments = watchRiskAssessments || [];
                                    form.setValue("riskAssessments", [...currentAssessments, { name: "", completionDate: undefined, personResponsible: "" }], { shouldDirty: true });
                                  }}
                                  className="bg-purple-50 text-purple-600 border-purple-200 hover:bg-purple-100"
                                >
                                  Add Risk Assessment
                                </Button>
                              </div>

                              {watchRiskAssessments?.length === 0 && (
                                <div className="text-center py-4 text-slate-500">
                                  No risk assessments added. Click "Add Risk Assessment" to add one.
                                </div>
                              )}

                              <div className="grid grid-cols-3 gap-4 mb-2 px-3">
                                <div className="font-medium text-sm text-gray-600">Risk Assessment Name:</div>
                                <div className="font-medium text-sm text-gray-600">Due Date</div>
                                <div className="font-medium text-sm text-gray-600">Person Responsible</div>
                              </div>

                              {watchRiskAssessments?.map((_, index) => (
                                <div key={index} className="grid grid-cols-3 gap-4 items-center mb-3 pb-3 border-b last:border-0 bg-white p-3 rounded-md shadow-sm">
                                  <FormField
                                    control={form.control}
                                    name={`riskAssessments.${index}.name`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <FormControl>
                                          <Input
                                            placeholder="Enter risk assessment name..."
                                            {...field}
                                            className="h-10"
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`riskAssessments.${index}.completionDate`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant={"outline"}
                                                className={cn(
                                                  "w-full pl-3 text-left font-normal h-10",
                                                  !field.value && "text-muted-foreground"
                                                )}
                                              >
                                                {field.value ? (
                                                  format(field.value, "dd-MM-yyyy")
                                                ) : (
                                                  <span>Select date</span>
                                                )}
                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                              mode="single"
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              initialFocus
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <div className="flex items-center gap-2">
                                    <FormField
                                      control={form.control}
                                      name={`riskAssessments.${index}.personResponsible`}
                                      render={({ field }) => (
                                        <FormItem className="flex-1 mb-0">
                                          <Select onValueChange={field.onChange} value={field.value || ""}>
                                            <FormControl>
                                              <SelectTrigger className="h-10">
                                                <SelectValue placeholder="Choose" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              {loadingUsers ? (
                                                <div className="p-2 text-sm text-gray-500">Loading users...</div>
                                              ) : locationUsers.length > 0 ? (
                                                locationUsers.map((user) => (
                                                  <SelectItem key={user.value} value={user.value}>
                                                    {user.label}
                                                  </SelectItem>
                                                ))
                                              ) : (
                                                <div className="p-2 text-sm text-gray-500">No users found</div>
                                              )}
                                            </SelectContent>
                                          </Select>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-10 w-10 rounded-full text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                      onClick={() => {
                                        const currentAssessments = watchRiskAssessments || [];
                                        const updatedAssessments = currentAssessments.filter((_, i) => i !== index);
                                        form.setValue("riskAssessments", updatedAssessments, { shouldDirty: true });
                                      }}
                                    >
                                      <X className="h-5 w-5" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Declaration Section */}
                        <div className="border-2 rounded-lg p-5 mb-8 bg-blue-50 border-blue-300 shadow-md">
                          <h3 className="text-xl font-bold mb-4 text-blue-800 flex items-center border-b border-blue-200 pb-3">
                            <Check className="mr-2 h-6 w-6 text-blue-600" />
                            Investigation Declaration
                          </h3>
                          <div className="bg-white p-4 rounded-md border border-blue-100">
                            <p className="text-base text-gray-800 leading-relaxed">
                              I confirm that the initial investigation and analysis of this incident have been completed, with corrective actions identified to address the root cause(s). This incident has been documented for further trend monitoring and analysis. Additional actions may be introduced by relevant stakeholders as needed to further strengthen controls and prevent recurrence.
                            </p>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                  </>
                )}

                {/* Detailed Investigation & Analysis Tab - shown for comprehensive workflow stage */}

                {workflowStage === 'comprehensive' && (
                  <TabsContent value="comprehensive-investigation" className="space-y-6">
                    <div className="form-section">
                      <div className={formSectionTitleClass}>
                        <div className={formSectionIconClass}>
                          <Microscope size={20} />
                        </div>
                        <h2 className="text-xl font-semibold">Detailed Investigation & Analysis</h2>
                      </div>

                      {/* Information banners */}
                      {incident?.investigator && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 shadow-sm">
                          <div className="flex items-center gap-3">
                            <div className="bg-blue-100 p-2 rounded-full">
                              <Microscope size={20} className="text-blue-600" />
                            </div>
                            <div>
                              <h3 className="text-lg font-bold text-blue-800">Lead Investigator Assigned</h3>
                              <p className="text-blue-700">
                                <strong>{getLeadInvestigatorLabel(incident?.investigator?.firstName)}</strong> has been assigned as the lead investigator for this incident.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}



                      {/* Use the ComprehensiveInvestigationForm component with isNested=true */}
                      <ComprehensiveInvestigationForm
                        incident={incident}
                        isNested={true}
                        onSubmit={(data) => {
                          // Update the form with the data from the ComprehensiveInvestigationForm
                          console.log("IncidentActionDialog - Detailed Investigation & Analysis onSubmit callback called with data:", data);
                          form.setValue('comprehensiveInvestigation', data, { shouldValidate: true });
                          console.log("IncidentActionDialog - After setting comprehensive investigation form value, form values:", form.getValues());
                        }}
                      />
                    </div>
                  </TabsContent>
                )}

                {userRole === 'reviewer' && workflowStage !== 'investigation' && (
                  <TabsContent value="preview" className="space-y-6">
                    <div className="form-section">
                      {/* Review Banner */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 shadow-sm">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <FileText size={24} className="text-blue-600" />
                          </div>
                          <div>
                            <h2 className="text-xl font-bold text-blue-800">Final Review</h2>
                            <p className="text-blue-700">
                              Please carefully review all information before finalizing this incident and moving it to the investigation stage.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className={formSectionTitleClass}>
                        <div className={formSectionIconClass}>
                          <FileText size={20} />
                        </div>
                        <h2 className="text-xl font-semibold">Preview Incident Details</h2>
                      </div>

                      {/* Incident Details Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Check size={18} className="mr-2 text-green-600" />
                          Incident Details
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-4">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Title</p>
                            <p className="font-semibold text-gray-900">{form.getValues("incidentTitle")}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Date & Time</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("incidentDate") ? format(form.getValues("incidentDate"), "PPP 'at' p") : "N/A"}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Type</p>
                            <p className="font-semibold text-gray-900">{getIncidentTypeLabel(form.getValues("incidentType"))}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Category</p>
                            <p className="font-semibold text-gray-900">{getIncidentCategoryLabel(form.getValues("incidentCategory"), incidentTypes)}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 mb-4">
                          <p className="text-sm font-medium text-gray-500 mb-1">Description</p>
                          <p className="font-semibold text-gray-900">{form.getValues("description")}</p>
                        </div>
                        <div className="bg-white p-4 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Location Details
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <div>
                              <p className="text-xs font-medium text-gray-500">Country</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.country")}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">City</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.city") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Business Unit</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.businessUnit") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Project/DC Ops</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.projectDcOps") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Level and Location</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.levelAndLocation") || "N/A"}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Classification Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <AlertTriangle size={18} className="mr-2 text-amber-600" />
                          Classification
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Work Related</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.isWorkRelated") === true ?
                                <span className="text-green-600">Yes</span> :
                                form.getValues("injuryClassification.isWorkRelated") === false ?
                                  <span className="text-red-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Loss of Consciousness</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.lossOfConsciousness") === true ?
                                <span className="text-red-600">Yes</span> :
                                form.getValues("injuryClassification.lossOfConsciousness") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Dangerous Occurrence</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.isDangerousOccurrence") === true ?
                                <span className="text-red-600">Yes</span> :
                                form.getValues("injuryClassification.isDangerousOccurrence") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                        </div>

                        <div className="bg-white p-4 rounded-md border border-gray-100 mb-5">
                          <p className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                            <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                            Injury Classification
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                            <div>
                              <p className="text-xs font-medium text-gray-500">Fatality</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isFatality") === true ?
                                  <span className="text-red-600">Yes</span> :
                                  form.getValues("injuryClassification.isFatality") === false ?
                                    <span className="text-green-600">No</span> :
                                    <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Permanent Disability</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isPermanentDisability") === true ?
                                  <span className="text-red-600">Yes</span> :
                                  form.getValues("injuryClassification.isPermanentDisability") === false ?
                                    <span className="text-green-600">No</span> :
                                    <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Lost Time Incident</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isLostTimeIncident") === true ?
                                  <span className="text-amber-600">Yes</span> :
                                  form.getValues("injuryClassification.isLostTimeIncident") === false ?
                                    <span className="text-green-600">No</span> :
                                    <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Medical Treatment</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isMedicalTreatment") === true ?
                                  <span className="text-amber-600">Yes</span> :
                                  form.getValues("injuryClassification.isMedicalTreatment") === false ?
                                    <span className="text-green-600">No</span> :
                                    <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">First Aid</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isFirstAid") === true ?
                                  <span className="text-blue-600">Yes</span> :
                                  form.getValues("injuryClassification.isFirstAid") === false ?
                                    <span className="text-green-600">No</span> :
                                    <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Property Damage</p>
                          <p className="font-semibold">
                            {form.getValues("propertyDamage") ?
                              <span className="text-amber-600">Yes</span> :
                              <span className="text-green-600">No</span>}
                          </p>
                          {form.getValues("propertyDamage") && (
                            <div className="mt-2 pl-3 border-l-2 border-amber-200">
                              <p className="text-xs font-medium text-gray-500">Property Damage Details</p>
                              <p className="font-medium text-gray-900">{form.getValues("propertyDamageDetails") || "N/A"}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Conditions Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Info size={18} className="mr-2 text-blue-600" />
                          Environmental Conditions
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Surface Type</p>
                            <p className="font-semibold text-gray-900">{form.getValues("surfaceType") ? getSurfaceTypeLabel(form.getValues("surfaceType"), surfaceTypes) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Surface Condition</p>
                            <p className="font-semibold text-gray-900">{form.getValues("surfaceCondition") ? getSurfaceConditionLabel(form.getValues("surfaceCondition"), surfaceConditions) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Lighting</p>
                            <p className="font-semibold text-gray-900">{form.getValues("lighting") ? getLightingLabel(form.getValues("lighting"), lightings) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Weather Condition</p>
                            <p className="font-semibold text-gray-900">{form.getValues("weatherCondition") ? getWeatherConditionLabel(form.getValues("weatherCondition"), weatherConditions) : "Not Specified"}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Reportable to Authorities</p>
                          <p className="font-semibold">
                            {form.getValues("reportableToAuthorities") ?
                              <span className="text-amber-600">Yes</span> :
                              <span className="text-green-600">No</span>}
                          </p>
                          {form.getValues("reportableToAuthorities") && (
                            <div className="mt-2 pl-3 border-l-2 border-amber-200">
                              <p className="text-xs font-medium text-gray-500">Reporting Details</p>
                              <p className="font-medium text-gray-900">{form.getValues("reportableDetails") || "N/A"}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions Taken Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Check size={18} className="mr-2 text-green-600" />
                          Actions Taken
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Action Date</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("immediateActionDate") ? format(form.getValues("immediateActionDate"), "PPP") : "N/A"}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Legal Classification</p>
                            <p className="font-semibold text-gray-900">{form.getValues("immediateActionLegalClassification") ? getLegalClassificationLabel(form.getValues("immediateActionLegalClassification")) : "Not Specified"}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 mb-4">
                          <p className="text-sm font-medium text-gray-500 mb-1">Actions Taken</p>
                          <p className="font-semibold text-gray-900">{form.getValues("immediateActionTaken") || "N/A"}</p>
                        </div>

                      </div>

                      {/* Attachments Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Upload size={18} className="mr-2 text-purple-600" />
                          Attachments
                        </h3>

                        {/* General Photos */}
                        <div className="bg-white p-3 rounded-md border border-gray-100 mb-3">
                          <p className="text-sm font-medium text-gray-500 mb-1">General Photos</p>
                          <p className="font-semibold text-gray-900 flex items-center">
                            {form.getValues("photos")?.length > 0 ? (
                              <>
                                <span className="inline-flex items-center justify-center w-6 h-6 mr-2 bg-purple-100 text-purple-700 rounded-full">
                                  {form.getValues("photos").length}
                                </span>
                                {`photo${form.getValues("photos").length > 1 ? 's' : ''} attached`}
                              </>
                            ) : (
                              <span className="text-gray-500">No photos attached</span>
                            )}
                          </p>
                        </div>

                        {/* Evidence Images */}
                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Evidence Images</p>
                          <p className="font-semibold text-gray-900 flex items-center">
                            {form.getValues("evidence")?.length > 0 ? (
                              <>
                                <span className="inline-flex items-center justify-center w-6 h-6 mr-2 bg-red-100 text-red-700 rounded-full">
                                  {form.getValues("evidence").length}
                                </span>
                                {`evidence image${form.getValues("evidence").length > 1 ? 's' : ''} attached`}
                              </>
                            ) : (
                              <span className="text-gray-500">No evidence images attached</span>
                            )}
                          </p>
                        </div>
                      </div>

                      {/* Incident Classification Result */}
                      <div className="border-2 rounded-lg p-5 mb-8 bg-amber-50 border-amber-300 shadow-md">
                        <h3 className="text-xl font-bold mb-4 text-amber-800 flex items-center border-b border-amber-200 pb-3">
                          <AlertTriangle className="mr-2 h-6 w-6 text-amber-600" />
                          Final Incident Classification
                        </h3>
                        <div className="bg-white p-4 rounded-md border border-amber-100">
                          {form.getValues("injuryClassification.isFatality") === true && (
                            <p className="font-bold text-xl text-red-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-red-100 text-red-700 rounded-full font-bold">5</span>
                              Level 5 – Critical Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isPermanentDisability") === true && form.getValues("injuryClassification.isFatality") !== true && (
                            <p className="font-bold text-xl text-orange-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-orange-100 text-orange-700 rounded-full font-bold">4</span>
                              Level 4 – High Severity Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isLostTimeIncident") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && (
                            <p className="font-bold text-xl text-amber-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-amber-100 text-amber-700 rounded-full font-bold">3</span>
                              Level 3 – LTI- Medium Severity Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isMedicalTreatment") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && (
                            <p className="font-bold text-xl text-yellow-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-yellow-100 text-yellow-700 rounded-full font-bold">2</span>
                              Level 2 – Low Severity Incident (MTI)
                            </p>
                          )}
                          {form.getValues("injuryClassification.isFirstAid") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && form.getValues("injuryClassification.isMedicalTreatment") !== true && (
                            <p className="font-bold text-xl text-blue-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-blue-100 text-blue-700 rounded-full font-bold">1</span>
                              Level 1 – Very Low Severity Incident (FAI)
                            </p>
                          )}
                          {form.getValues("injuryClassification.isFatality") === false && form.getValues("injuryClassification.isPermanentDisability") === false && form.getValues("injuryClassification.isLostTimeIncident") === false && form.getValues("injuryClassification.isMedicalTreatment") === false && form.getValues("injuryClassification.isFirstAid") === false && (
                            <p className="font-bold text-xl text-green-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-green-100 text-green-700 rounded-full font-bold">0</span>
                              NEAR MISS
                            </p>
                          )}
                        </div>
                      </div>



                      {/* Declaration Section */}
                      <div className="border-2 rounded-lg p-5 mb-8 bg-blue-50 border-blue-300 shadow-md">
                        <h3 className="text-xl font-bold mb-4 text-blue-800 flex items-center border-b border-blue-200 pb-3">
                          <Check className="mr-2 h-6 w-6 text-blue-600" />
                          Declaration
                        </h3>
                        <div className="flex items-start space-x-4 p-4 bg-white rounded-md border border-blue-100">
                          <div className="flex-shrink-0 mt-0.5">
                            <Checkbox
                              id="declaration"
                              checked={declarationChecked}
                              onCheckedChange={(checked) => {
                                setDeclarationChecked(checked === true);
                              }}
                              className="h-5 w-5 border-2 border-blue-300"
                            />
                          </div>
                          <div>
                            <label
                              htmlFor="declaration"
                              className="text-base font-medium text-gray-800 leading-relaxed block"
                            >
                              I confirm that the above information is accurate to the best of my knowledge.
                            </label>
                            <p className="text-sm text-gray-600 mt-1">
                              Once submitted, the incident will be moved to the investigation stage. The details can be updated as needed during the investigation process.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                )}

                <div className="flex justify-between pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Define tabs based on workflow stage
                      let tabs: string[] = [];
                      if (workflowStage === 'comprehensive') {
                        tabs = ["comprehensive-investigation"];
                      } else if (workflowStage === 'investigation') {
                        tabs = ["investigation", "control-measures"];
                      } else if (userRole === 'reviewer') {
                        tabs = ["details", "classification", "conditions", "actions", "attachments", "preview"];
                      } else {
                        tabs = ["details", "classification", "conditions", "actions", "attachments"];
                      }

                      const currentTabIndex = tabs.indexOf(activeTab);
                      if (currentTabIndex > 0) {
                        setActiveTab(tabs[currentTabIndex - 1]);
                      }
                    }}
                    disabled={workflowStage === 'comprehensive' ?
                      activeTab === "comprehensive-investigation" :
                      workflowStage === 'investigation' ?
                        activeTab === "investigation" :
                        activeTab === "details"}
                    className="bg-white"
                  >
                    Previous
                  </Button>

                  {workflowStage === 'comprehensive' ? (
                    // Detailed Investigation & Analysis navigation logic (Step 4)
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={(e) => {
                          // Prevent default form submission
                          e.preventDefault();
                          saveChanges();
                        }}
                        className="bg-white"
                      >
                        Save
                      </Button>
                      <Button
                        type="submit"
                        onClick={(e) => {
                          // Submit the comprehensive investigation report
                          form.handleSubmit(onSubmit)(e);
                        }}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Submit Detailed Investigation & Analysis
                      </Button>
                    </div>
                  ) : workflowStage === 'investigation' ? (
                    // Investigation navigation logic (Step 3)
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={(e) => {
                          // Prevent default form submission
                          e.preventDefault();
                          saveChanges();
                        }}
                        className="bg-white"
                      >
                        Save
                      </Button>
                      {activeTab !== "control-measures" ? (
                        <Button
                          type="button"
                          onClick={(e) => {
                            // Prevent default form submission
                            e.preventDefault();

                            const tabs = ["investigation", "control-measures"];
                            const currentTabIndex = tabs.indexOf(activeTab);
                            if (currentTabIndex < 1) {
                              // Navigate to the next tab
                              setActiveTab(tabs[currentTabIndex + 1]);
                            }
                          }}
                          className="bg-primary hover:bg-primary/90"
                        >
                          Next
                        </Button>
                      ) : (
                        <Button
                          type="submit"
                          onClick={(e) => {
                            // Submit the investigation report for both reviewers and reporters
                            form.handleSubmit(onSubmit)(e);
                          }}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Submit Investigation
                        </Button>
                      )}
                    </div>
                  ) : userRole === 'reviewer' ? (
                    // Reviewer navigation logic with preview tab
                    <>
                      {activeTab !== "preview" ? (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();

                              const tabs = ["details", "classification", "conditions", "actions", "attachments", "preview"];
                              const currentTabIndex = tabs.indexOf(activeTab);
                              if (currentTabIndex < 5) {
                                // Navigate to the next tab
                                setActiveTab(tabs[currentTabIndex + 1]);
                              }
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            {activeTab === "attachments" ? "Review & Finalize" : "Next"}
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveReporterChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="submit"
                            onClick={async (e) => {
                              e.preventDefault();
                              console.log("🔄 Submit button clicked");
                              console.log("🔄 Declaration checked:", declarationChecked);
                              console.log("🔄 Form state:", form.formState);
                              console.log("🔄 Form errors:", form.formState.errors);
                              console.log("🔄 Form values:", form.getValues());

                              // Check if form is valid
                              const isValid = await form.trigger();
                              console.log("🔄 Form validation result:", isValid);

                              if (!isValid) {
                                console.error("❌ Form validation failed");
                                toast.error("Please fix the form errors before submitting");
                                return;
                              }

                              if (!declarationChecked) {
                                console.error("❌ Declaration not checked");
                                toast.error("Please check the declaration before submitting");
                                return;
                              }

                              // Submit the form
                              try {
                                console.log("🔄 Calling onSubmit...");
                                await onSubmit(form.getValues());
                                console.log("✅ onSubmit completed successfully");
                              } catch (error) {
                                console.error("❌ onSubmit failed:", error);
                                toast.error("Submission failed. Please try again.");
                              }
                            }}
                            className="bg-green-600 hover:bg-green-700"
                            disabled={!declarationChecked}
                          >
                            Submit
                          </Button>
                        </div>
                      )}
                    </>
                  ) : (
                    // Reporter navigation logic without preview tab
                    <>
                      {activeTab !== "attachments" ? (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              if (userRole === 'reporter') {
                                saveReporterChanges();
                              } else {
                                saveChanges();
                              }
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();

                              const tabs = ["details", "classification", "conditions", "actions", "attachments"];
                              const currentTabIndex = tabs.indexOf(activeTab);
                              if (currentTabIndex < 4) {
                                // Navigate to the next tab
                                setActiveTab(tabs[currentTabIndex + 1]);
                              }
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            Next
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              if (userRole === 'reporter') {
                                saveReporterChanges();
                              } else {
                                saveChanges();
                              }
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              if (userRole === 'reporter') {
                                saveReporterChanges();
                              } else {
                                saveChanges();
                              }
                              onOpenChange(false);
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            Save Changes
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </form>
            </Form>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncidentActionDialog;
