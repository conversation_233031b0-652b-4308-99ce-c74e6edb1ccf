import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ClipboardList, User } from "lucide-react";
import { ApiService } from "@/lib/utils";
import { toast } from "sonner";

interface InvestigationStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incident: any | null;
  onSave: (data: { leadInvestigator: string; remarks: string }) => void;
  onStartInvestigation?: (data: { leadInvestigator: string; remarks: string }) => void;
}

const InvestigationStatusDialog: React.FC<InvestigationStatusDialogProps> = ({
  open,
  onOpenChange,
  incident,
  onSave,
  onStartInvestigation,
}) => {
  const [leadInvestigator, setLeadInvestigator] = useState<string>("");
  const [remarks, setRemarks] = useState<string>("");
  const [leadInvestigators, setLeadInvestigators] = useState<{ value: string, label: string }[]>([]);
  const [loadingInvestigators, setLoadingInvestigators] = useState(false);

  // Function to load lead investigators from API
  const loadLeadInvestigators = async () => {
    try {
      setLoadingInvestigators(true);

      // Extract location data from incident - try multiple sources
      const appDetails = incident?.applicationDetails || {};
      const locationData = {
        locationOneId: appDetails.locationOneId || incident?.locationOneId || incident?.incidentData?.locationOneId,
        locationTwoId: appDetails.locationTwoId || incident?.locationTwoId || incident?.incidentData?.locationTwoId,
        locationThreeId: appDetails.locationThreeId || incident?.locationThreeId || incident?.incidentData?.locationThreeId,
        locationFourId: appDetails.locationFourId || incident?.locationFourId || incident?.incidentData?.locationFourId
      };

      const response = await ApiService.getLeadInvestigatorList(locationData);

      // Check different possible response structures
      let userData = null;
      if (response?.data) {
        userData = response.data;
      } else if (Array.isArray(response)) {
        userData = response;
      }

      const investigators = userData?.map((user: any) => ({
        value: user.id,
        label: user.firstName || user.name || user.username || `User ${user.id}`
      })) || [];

      setLeadInvestigators(investigators);
    } catch (error) {
      console.error("❌ Failed to load lead investigators:", error);
      toast.error("Failed to load lead investigators");
      setLeadInvestigators([]);
    } finally {
      setLoadingInvestigators(false);
    }
  };

  // Reset form and load data when dialog opens
  React.useEffect(() => {
    if (open) {
      setLeadInvestigator(incident?.leadInvestigator || "");
      setRemarks(incident?.investigationRemarks || "");

      // Load lead investigators if we have location data
      if (incident?.applicationDetails?.locationOneId || incident?.applicationDetails?.locationTwoId ||
          incident?.applicationDetails?.locationThreeId || incident?.applicationDetails?.locationFourId) {
        loadLeadInvestigators();
      } else {
        // Try alternative location sources
        const altLocationData = {
          locationOneId: incident?.locationOneId || incident?.incidentData?.locationOneId,
          locationTwoId: incident?.locationTwoId || incident?.incidentData?.locationTwoId,
          locationThreeId: incident?.locationThreeId || incident?.incidentData?.locationThreeId,
          locationFourId: incident?.locationFourId || incident?.incidentData?.locationFourId
        };

        if (altLocationData.locationOneId || altLocationData.locationTwoId ||
            altLocationData.locationThreeId || altLocationData.locationFourId) {
          loadLeadInvestigators();
        }
      }
    }
  }, [open, incident]);

  const handleSave = async () => {
    if (!leadInvestigator.trim()) {
      toast.error("Please select a lead investigator");
      return;
    }

    try {
      const requestData = {
        investigatorId: leadInvestigator,
        investigationRemarks: remarks.trim()
      };

      await ApiService.triggerInvestigation(incident.id, requestData);

      toast.success("Investigation assigned successfully", {
        description: `Lead investigator assigned for incident ${incident.maskId || incident.id}`,
      });

      // Call the parent onSave for any additional local state updates
      onSave({
        leadInvestigator,
        remarks: remarks.trim(),
      });

      onOpenChange(false);
    } catch (error) {
      console.error("❌ Failed to trigger investigation:", error);
      toast.error("Failed to assign investigation", {
        description: "Please try again or contact support",
      });
    }
  };

  const handleStartInvestigation = async () => {
    if (!leadInvestigator.trim()) {
      toast.error("Please select a lead investigator");
      return;
    }

    try {
      const requestData = {
        investigatorId: leadInvestigator,
        investigationRemarks: remarks.trim()
      };

      await ApiService.triggerInvestigation(incident.id, requestData);

      toast.success("Investigation started successfully", {
        description: `Comprehensive investigation started for incident ${incident.maskId || incident.id}`,
      });

      // Call the parent onStartInvestigation for any additional local state updates
      if (onStartInvestigation) {
        onStartInvestigation({
          leadInvestigator,
          remarks: remarks.trim(),
        });
      }

      onOpenChange(false);
    } catch (error) {
      console.error("❌ Failed to start investigation:", error);
      toast.error("Failed to start investigation", {
        description: "Please try again or contact support",
      });
    }
  };

  const handleCancel = () => {
    setLeadInvestigator("");
    setRemarks("");
    onOpenChange(false);
  };

  if (!incident) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md w-[90vw]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5 text-blue-600" />
            Investigation Assignment
          </DialogTitle>
          <DialogDescription>
            Assign a lead investigator for incident {incident.maskId}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Lead Investigator Selection */}
          <div className="space-y-2">
            <Label htmlFor="leadInvestigator" className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Lead Investigator *
            </Label>
            <Select value={leadInvestigator} onValueChange={setLeadInvestigator}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={loadingInvestigators ? "Loading investigators..." : "Select a lead investigator"} />
              </SelectTrigger>
              <SelectContent>
                {loadingInvestigators ? (
                  <div className="p-2 text-sm text-gray-500">Loading investigators...</div>
                ) : leadInvestigators.length > 0 ? (
                  leadInvestigators.map((investigator) => (
                    <SelectItem key={investigator.value} value={investigator.value}>
                      {investigator.label}
                    </SelectItem>
                  ))
                ) : (
                  <div className="p-2 text-sm text-gray-500">No investigators found</div>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Remarks Text Box */}
          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-sm font-medium">
              Remarks
            </Label>
            <Textarea
              id="remarks"
              placeholder="Add any additional remarks or instructions for the investigation..."
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={500}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Optional field for additional context</span>
              <span>{remarks.length}/500 characters</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          {onStartInvestigation ? (
            <Button
              onClick={handleStartInvestigation}
              disabled={!leadInvestigator.trim()}
              className="bg-green-600 hover:bg-green-700"
            >
              Submit & Start Investigation
            </Button>
          ) : (
            <Button
              onClick={handleSave}
              disabled={!leadInvestigator.trim()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Save Assignment
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvestigationStatusDialog;
