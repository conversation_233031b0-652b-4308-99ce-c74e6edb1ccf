import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService, getToken } from '@/lib/utils';
import { toast } from 'sonner';
import { incidentTypes } from '@/utils/formData';

// Helper function to decode JWT and check expiration
const decodeJWT = (token: string) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to decode JWT:', error);
    return null;
  }
};

const checkTokenExpiration = (token: string) => {
  const decoded = decodeJWT(token);
  if (!decoded || !decoded.exp) {
    return { isExpired: true, message: 'Invalid token or no expiration' };
  }

  const now = Math.floor(Date.now() / 1000);
  const isExpired = now >= decoded.exp;
  const expirationDate = new Date(decoded.exp * 1000);

  return {
    isExpired,
    expirationDate,
    message: isExpired ? 'Token is expired' : 'Token is valid',
    timeUntilExpiry: isExpired ? 0 : decoded.exp - now
  };
};

const ReviewerActionsApiTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actions, setActions] = useState<any>(null);
  const [categories, setCategories] = useState<any>(null);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [categoriesError, setCategoriesError] = useState<string | null>(null);
  const [directFetchResult, setDirectFetchResult] = useState<any>(null);
  const [directFetchLoading, setDirectFetchLoading] = useState(false);
  const [directFetchError, setDirectFetchError] = useState<string | null>(null);
  const [alternativeEndpoints, setAlternativeEndpoints] = useState<any>(null);
  const [testingAlternatives, setTestingAlternatives] = useState(false);

  // Get current token using centralized token management
  const token = getToken();
  const tokenStatus = checkTokenExpiration(token || '');

  const testReviewerActionsEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setActions(null);

    try {
      console.log('🔄 Testing /actions/get/INCIDENT endpoint...');
      const response = await ApiService.getReviewerActions();
      console.log('✅ Reviewer actions test successful:', response);
      setActions(response);
      toast.success('Reviewer actions loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Reviewer actions test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load reviewer actions: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testIncidentCategoriesEndpoint = async () => {
    setLoadingCategories(true);
    setCategoriesError(null);
    setCategories(null);

    try {
      console.log('🔄 Testing /incident-circumstance-categories endpoint...');
      console.log('🔗 Full URL:', 'https://dev.stt-user.acuizen.com/incident-circumstance-categories');
      console.log('🔑 Using token: Token present');

      const response = await ApiService.getIncidentCircumstanceCategories();
      console.log('✅ Incident categories test successful:', response);
      console.log('📊 Response type:', typeof response);
      console.log('📊 Is array:', Array.isArray(response));

      if (Array.isArray(response)) {
        console.log('📊 Categories count:', response.length);
        response.forEach((cat, index) => {
          console.log(`📋 Category ${index + 1}:`, cat);
        });
      }

      setCategories(response);
      toast.success('Incident categories loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Incident categories test failed:', errorMessage);
      console.error('❌ Full error object:', err);
      setCategoriesError(errorMessage);

      // Show fallback categories
      console.log('🔄 Using fallback incident categories from formData.ts');
      const fallbackCategories = incidentTypes.map(type => ({ id: type.value, name: type.label }));
      console.log('📋 Fallback categories:', fallbackCategories);
      setCategories(fallbackCategories);

      toast.error(`Failed to load incident categories: ${errorMessage}. Using fallback data.`);
    } finally {
      setLoadingCategories(false);
    }
  };

  const testDirectFetch = async () => {
    setDirectFetchLoading(true);
    setDirectFetchError(null);
    setDirectFetchResult(null);

    try {
      console.log('🔄 Testing direct fetch to /incident-circumstance-categories...');
      const response = await fetch('https://dev.stt-user.acuizen.com/incident-circumstance-categories', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log('📊 Direct fetch response status:', response.status);
      console.log('📊 Direct fetch response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Direct fetch HTTP error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Direct fetch successful:', data);
      setDirectFetchResult(data);
      toast.success('Direct fetch successful!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Direct fetch failed:', errorMessage);
      setDirectFetchError(errorMessage);
      toast.error(`Direct fetch failed: ${errorMessage}`);
    } finally {
      setDirectFetchLoading(false);
    }
  };

  const testAlternativeEndpoints = async () => {
    setTestingAlternatives(true);
    setAlternativeEndpoints(null);

    const endpoints = [
      '/incident-categories',
      '/incident-types',
      '/circumstance-categories',
      '/categories',
      '/incident-circumstance-types',
      '/incident-circumstance-descriptions'
    ];

    const results: any = {};

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Testing alternative endpoint: ${endpoint}`);
        const response = await fetch(`https://dev.stt-user.acuizen.com${endpoint}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          mode: 'cors',
        });

        if (response.ok) {
          const data = await response.json();
          results[endpoint] = { success: true, data, status: response.status };
          console.log(`✅ ${endpoint} SUCCESS:`, data);
        } else {
          results[endpoint] = { success: false, status: response.status, error: await response.text() };
          console.log(`❌ ${endpoint} FAILED:`, response.status);
        }
      } catch (error) {
        results[endpoint] = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
        console.log(`❌ ${endpoint} ERROR:`, error);
      }
    }

    setAlternativeEndpoints(results);
    setTestingAlternatives(false);

    const successfulEndpoints = Object.entries(results).filter(([_, result]: [string, any]) => result.success);
    if (successfulEndpoints.length > 0) {
      toast.success(`Found ${successfulEndpoints.length} working alternative endpoints!`);
    } else {
      toast.error('No alternative endpoints found working');
    }
  };

  return (
    <>
      {/* Token Status Card */}
      <Card className="w-full max-w-4xl mx-auto mb-6">
        <CardHeader>
          <CardTitle>JWT Token Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`p-4 rounded ${tokenStatus.isExpired ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
            <h3 className={`font-medium mb-2 ${tokenStatus.isExpired ? 'text-red-800' : 'text-green-800'}`}>
              {tokenStatus.message}
            </h3>
            <div className={`text-sm ${tokenStatus.isExpired ? 'text-red-700' : 'text-green-700'}`}>
              <p><strong>Expiration Date:</strong> {tokenStatus.expirationDate?.toLocaleString()}</p>
              {!tokenStatus.isExpired && (
                <p><strong>Time until expiry:</strong> {Math.floor(tokenStatus.timeUntilExpiry / 3600)} hours, {Math.floor((tokenStatus.timeUntilExpiry % 3600) / 60)} minutes</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Reviewer Actions API Test (/actions/get/INCIDENT)
            <Button
              onClick={testReviewerActionsEndpoint}
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Loading...' : 'Test /actions/get/INCIDENT Endpoint'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Endpoint:</h3>
            <code className="bg-gray-100 p-2 rounded text-sm block">
              GET https://dev.stt-user.acuizen.com/actions/get/INCIDENT
            </code>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-4">
              <h3 className="font-medium text-red-800 mb-2">Error:</h3>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {actions && (
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <h3 className="font-medium text-green-800 mb-2">Success! Response:</h3>
              <div className="text-sm text-green-700">
                <p className="mb-2">
                  <strong>Type:</strong> {Array.isArray(actions) ? 'Array' : typeof actions}
                </p>
                {Array.isArray(actions) && (
                  <p className="mb-2">
                    <strong>Count:</strong> {actions.length} incidents
                  </p>
                )}
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">View Raw Response</summary>
                  <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-96">
                    {JSON.stringify(actions, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          )}

          <div className="text-sm text-gray-600">
            <h3 className="font-medium mb-2">Expected Response:</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>Array of incident objects requiring reviewer action</li>
              <li>Each incident should have fields like id, title, status, etc.</li>
              <li>Should include incident details for reviewer actions</li>
              <li>API accepts no parameters - returns all reviewer actions</li>
              <li>Should be called only once to prevent repeated API calls</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Incident Categories API Test */}
      <Card className="w-full max-w-4xl mx-auto mt-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Incident Categories API Test (/incident-circumstance-categories)
          <Button
            onClick={testIncidentCategoriesEndpoint}
            disabled={loadingCategories}
            variant="outline"
          >
            {loadingCategories ? 'Loading...' : 'Test Categories Endpoint'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/incident-circumstance-categories
          </code>
        </div>

        {categoriesError && (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h3 className="font-medium text-red-800 mb-2">Error:</h3>
            <p className="text-red-700 text-sm">{categoriesError}</p>
          </div>
        )}

        {categories && (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <h3 className="font-medium text-green-800 mb-2">Success! Response:</h3>
            <div className="text-sm text-green-700">
              <p className="mb-2">
                <strong>Type:</strong> {Array.isArray(categories) ? 'Array' : typeof categories}
              </p>
              {Array.isArray(categories) && (
                <p className="mb-2">
                  <strong>Count:</strong> {categories.length} categories
                </p>
              )}
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">View Raw Response</summary>
                <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-96">
                  {JSON.stringify(categories, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <h3 className="font-medium mb-2">Expected Response:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Array of incident category objects</li>
            <li>Each category should have id and name fields</li>
            <li>Should include categories like Health, Safety, Environmental</li>
            <li>Used for incident type dropdown in forms</li>
          </ul>
        </div>
      </CardContent>
    </Card>

      {/* Direct Fetch Test */}
      <Card className="w-full max-w-4xl mx-auto mt-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Direct Fetch Test (Bypass ApiService)
            <Button
              onClick={testDirectFetch}
              disabled={directFetchLoading}
              variant="outline"
            >
              {directFetchLoading ? 'Loading...' : 'Test Direct Fetch'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Direct Fetch URL:</h3>
            <code className="bg-gray-100 p-2 rounded text-sm block">
              GET https://dev.stt-user.acuizen.com/incident-circumstance-categories
            </code>
          </div>

          {directFetchError && (
            <div className="bg-red-50 border border-red-200 rounded p-4">
              <h3 className="font-medium text-red-800 mb-2">Direct Fetch Error:</h3>
              <p className="text-red-700 text-sm">{directFetchError}</p>
            </div>
          )}

          {directFetchResult && (
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <h3 className="font-medium text-green-800 mb-2">Direct Fetch Success!</h3>
              <div className="text-sm text-green-700">
                <p className="mb-2">
                  <strong>Type:</strong> {Array.isArray(directFetchResult) ? 'Array' : typeof directFetchResult}
                </p>
                {Array.isArray(directFetchResult) && (
                  <p className="mb-2">
                    <strong>Count:</strong> {directFetchResult.length} categories
                  </p>
                )}
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">View Raw Response</summary>
                  <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-96">
                    {JSON.stringify(directFetchResult, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fallback Categories Display */}
      <Card className="w-full max-w-4xl mx-auto mt-6">
        <CardHeader>
          <CardTitle>Fallback Incident Categories (from formData.ts)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded p-4">
            <h3 className="font-medium text-blue-800 mb-2">Available Fallback Categories:</h3>
            <div className="text-sm text-blue-700">
              <pre className="bg-white p-2 rounded border text-xs">
                {JSON.stringify(incidentTypes, null, 2)}
              </pre>
              <p className="mt-2">
                <strong>Note:</strong> These categories are used as fallback when the API fails.
                According to user preferences, 'Environmental' category should be excluded from displays.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alternative Endpoints Test */}
      <Card className="w-full max-w-4xl mx-auto mt-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Alternative Endpoints Test
            <Button
              onClick={testAlternativeEndpoints}
              disabled={testingAlternatives}
              variant="outline"
            >
              {testingAlternatives ? 'Testing...' : 'Test Alternative Endpoints'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Testing these endpoints:</h3>
            <ul className="text-sm text-gray-600 list-disc list-inside">
              <li>/incident-categories</li>
              <li>/incident-types</li>
              <li>/circumstance-categories</li>
              <li>/categories</li>
              <li>/incident-circumstance-types</li>
              <li>/incident-circumstance-descriptions</li>
            </ul>
          </div>

          {alternativeEndpoints && (
            <div className="space-y-2">
              <h3 className="font-medium">Results:</h3>
              {Object.entries(alternativeEndpoints).map(([endpoint, result]: [string, any]) => (
                <div key={endpoint} className={`p-3 rounded border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <div className="flex items-center justify-between">
                    <code className="text-sm">{endpoint}</code>
                    <span className={`text-sm font-medium ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                      {result.success ? '✅ SUCCESS' : '❌ FAILED'}
                    </span>
                  </div>
                  {result.success && result.data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm font-medium">View Response</summary>
                      <pre className="mt-1 bg-white p-2 rounded border text-xs overflow-auto max-h-32">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                  {!result.success && (
                    <div className="mt-1 text-sm text-red-600">
                      Status: {result.status} | Error: {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};

export default ReviewerActionsApiTest;
