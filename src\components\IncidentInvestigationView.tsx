import React from "react";
import { format } from "date-fns";
import { AlertTriangle, Info, Check, Upload, FileText, Microscope } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import InvestigationAnalysisForm from "./InvestigationAnalysisForm";

interface IncidentInvestigationViewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incident: any | null;
}

const IncidentInvestigationView: React.FC<IncidentInvestigationViewProps> = ({
  open,
  onOpenChange,
  incident,
}) => {
  const [activeTab, setActiveTab] = React.useState("details");

  if (!incident) {
    return null;
  }

  // Extract application details for comprehensive data access
  const appDetails = incident.applicationDetails || {};
  const incidentData = incident.incidentData || {};

  // Helper functions to get labels from values
  const getIncidentTypeLabel = (value: string) => {
    const types: Record<string, string> = {
      safety: "Safety",
      environmental: "Environmental",
      health: "Health",
      nearMiss: "Near Miss",
      propertyDamage: "Property Damage",
      security: "Security",
      quality: "Quality",
    };
    return types[value] || value;
  };

  const getIncidentCategoryLabel = (value: string) => {
    const categories: Record<string, string> = {
      fire: "Fire",
      slip: "Slip",
      fall: "Fall",
      electrical: "Electrical Hazard",
      chemical: "Chemical Spill",
      vehicle: "Vehicle Accident",
      machinery: "Machinery",
      tool: "Tool Related",
      ergonomic: "Ergonomic",
      other: "Other",
    };
    return categories[value] || value;
  };

  const getSurfaceTypeLabel = (value: string) => {
    const types: Record<string, string> = {
      concrete: "Concrete",
      carpet: "Carpet",
      tile: "Tile",
      wood: "Wood",
      metal: "Metal",
      asphalt: "Asphalt",
      grass: "Grass",
      other: "Other",
    };
    return types[value] || value;
  };

  const getSurfaceConditionLabel = (value: string) => {
    const conditions: Record<string, string> = {
      dry: "Dry",
      wet: "Wet",
      icy: "Icy",
      oily: "Oily/Slippery",
      damaged: "Damaged",
      uneven: "Uneven",
      other: "Other",
    };
    return conditions[value] || value;
  };

  const getLightingLabel = (value: string) => {
    const options: Record<string, string> = {
      good: "Good",
      adequate: "Adequate",
      poor: "Poor",
      none: "None",
    };
    return options[value] || value;
  };

  const getWeatherConditionLabel = (value: string) => {
    const conditions: Record<string, string> = {
      clear: "Clear",
      cloudy: "Cloudy",
      rain: "Rain",
      snow: "Snow",
      fog: "Fog",
      wind: "Windy",
      storm: "Storm",
      indoor: "Indoor (N/A)",
    };
    return conditions[value] || value;
  };

  const getLegalClassificationLabel = (value: string) => {
    const classifications: Record<string, string> = {
      negligence: "Negligence",
      regulatory: "Regulatory Violation",
      compliance: "Compliance Issue",
      contractual: "Contractual Breach",
      none: "No Legal Issues Identified",
    };
    return classifications[value] || value;
  };



  const sectionTitleClass = "flex items-center gap-2 mb-4";
  const sectionIconClass = "p-1.5 rounded-md bg-primary/10 text-primary";
  const labelClass = "text-sm font-medium text-gray-500";
  const valueClass = "text-base";
  const fieldContainerClass = "mb-4";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] overflow-y-auto p-0">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <DialogHeader className="p-6 pb-2">
          <DialogTitle className="text-2xl font-bold">
            Incident Investigation: {incident?.maskId || ''}
          </DialogTitle>
          <DialogDescription>
            This incident is under investigation. Review the details below.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 pb-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-7 mb-6">
              <TabsTrigger value="details">Incident Details</TabsTrigger>
              <TabsTrigger value="classification">Classification</TabsTrigger>
              <TabsTrigger value="conditions">Conditions</TabsTrigger>
              <TabsTrigger value="actions">Actions Taken</TabsTrigger>
              <TabsTrigger value="investigation">Investigation</TabsTrigger>
              <TabsTrigger value="attachments">Attachments</TabsTrigger>
              <TabsTrigger value="control-measures">Control Measures</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <Info size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Incident Details</h2>
                </div>

                <div className="space-y-6">
                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Incident Title</div>
                    <div className={valueClass}>{appDetails?.title || incidentData?.incidentTitle || incident.incidentTitle || incident.description || "N/A"}</div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Incident Date</div>
                      <div className={valueClass}>
                        {(() => {
                          const dateValue = appDetails?.date || incidentData?.incidentDate || incident.incidentDate;
                          return dateValue ? format(new Date(dateValue), "PPP") : "N/A";
                        })()}
                      </div>
                    </div>

                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Incident Time</div>
                      <div className={valueClass}>{appDetails?.time || incidentData?.incidentTime || incident.incidentTime || "N/A"}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Incident Type</div>
                      <div className={valueClass}>
                        {(() => {
                          const incidentType = appDetails?.incidentCircumstanceCategoryId || incidentData?.incidentType || incident.incidentType;
                          return incidentType ? getIncidentTypeLabel(incidentType) : "N/A";
                        })()}
                      </div>
                    </div>

                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Incident Category</div>
                      <div className={valueClass}>
                        {(() => {
                          const incidentCategory = appDetails?.incidentCircumstanceTypeId || incidentData?.incidentCategory || incident.incidentCategory;
                          return incidentCategory ? getIncidentCategoryLabel(incidentCategory) : "N/A";
                        })()}
                      </div>
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Description</div>
                    <div className={valueClass}>{appDetails?.description || incidentData?.description || incident.description || "N/A"}</div>
                  </div>

                  <div className="space-y-4 border p-4 rounded-md">
                    <h3 className="font-medium">Location</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Country</div>
                        <div className={valueClass}>
                          {appDetails?.locationOne?.name || incidentData?.locationCountry || incident.locationCountry || incident.country || "N/A"}
                        </div>
                      </div>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>City</div>
                        <div className={valueClass}>
                          {appDetails?.locationTwo?.name || incidentData?.locationCity || incident.locationCity || incident.city || "N/A"}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Business Unit</div>
                        <div className={valueClass}>
                          {appDetails?.locationThree?.name || incidentData?.locationBusinessUnit || incident.locationBusinessUnit || incident.workplaceActivity || "N/A"}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Project/DC Ops</div>
                        <div className={valueClass}>
                          {appDetails?.locationFour?.name || incidentData?.locationProject || incident.locationProject || incident.projectDcOps || "N/A"}
                        </div>
                      </div>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Level and Location</div>
                        <div className={valueClass}>
                          {appDetails?.incidentData?.locationDetails || incidentData?.locationDetails || incident.locationDetails || incident.levelAndLocation || "N/A"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="classification" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <AlertTriangle size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Classification of Potentially Serious Incident</h2>
                </div>

                <div className="space-y-6 border p-4 rounded-md">
                  <p className="text-sm text-muted-foreground italic">
                    Would this incident potentially result in:
                  </p>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>A fatality?</div>
                    <div className={valueClass}>
                      {incident.potentiallySerious?.couldResultInFatality ? "Yes" : "No"}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>An injury or occupational illness resulting in permanent disability?</div>
                    <div className={valueClass}>
                      {incident.potentiallySerious?.couldResultInPermanentDisability ? "Yes" : "No"}
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="font-medium mb-2">Potential Severity Level:</h3>
                    <div className={valueClass}>
                      <Badge variant="outline" className="text-base font-medium">
                        {incident.potentialSeverityLevel || "Not Applicable"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Property Damage</div>
                    <div className={valueClass}>{incident.propertyDamage ? "Yes" : "No"}</div>
                  </div>

                  {incident.propertyDamage && incident.propertyDamageDetails && (
                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Property Damage Details</div>
                      <div className={valueClass}>{incident.propertyDamageDetails}</div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="conditions" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <Info size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Environmental Conditions</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Surface Type</div>
                    <div className={valueClass}>
                      {(() => {
                        const surfaceType = appDetails?.surfaceTypeId || incidentData?.surfaceType || incident.surfaceType;
                        return surfaceType ? getSurfaceTypeLabel(surfaceType) : "N/A";
                      })()}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Surface Condition</div>
                    <div className={valueClass}>
                      {(() => {
                        const surfaceCondition = appDetails?.surfaceConditionId || incidentData?.surfaceCondition || incident.surfaceCondition;
                        return surfaceCondition ? getSurfaceConditionLabel(surfaceCondition) : "N/A";
                      })()}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Lighting</div>
                    <div className={valueClass}>
                      {(() => {
                        const lighting = appDetails?.lightingId || incidentData?.lighting || incident.lighting;
                        return lighting ? getLightingLabel(lighting) : "N/A";
                      })()}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Weather Condition</div>
                    <div className={valueClass}>
                      {(() => {
                        const weatherCondition = appDetails?.weatherConditionId || incidentData?.weatherCondition || incident.weatherCondition;
                        return weatherCondition ? getWeatherConditionLabel(weatherCondition) : "N/A";
                      })()}
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Is this incident reportable to local authority and/or customer?</div>
                    <div className={valueClass}>
                      {(() => {
                        const reportAuthority = appDetails?.reportAuthority || incidentData?.reportableToAuthorities || incident.reportableToAuthorities;
                        return reportAuthority === "true" || reportAuthority === true ? "Yes" : "No";
                      })()}
                    </div>
                  </div>

                  {(() => {
                    const reportAuthority = appDetails?.reportAuthority || incidentData?.reportableToAuthorities || incident.reportableToAuthorities;
                    const reportDetails = appDetails?.authorityName || incidentData?.reportableDetails || incident.reportableDetails;
                    return (reportAuthority === "true" || reportAuthority === true) && reportDetails && (
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Reporting Details</div>
                        <div className={valueClass}>{reportDetails}</div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="actions" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <Check size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Immediate Actions Taken</h2>
                </div>

                <div className="space-y-6">
                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Action Date</div>
                    <div className={valueClass}>
                      {(() => {
                        const actionDate = appDetails?.immediateActionDate || incidentData?.immediateActionDate || incident.immediateActionDate;
                        return actionDate ? format(new Date(actionDate), "PPP") : "N/A";
                      })()}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Actions Taken</div>
                    <div className={valueClass}>
                      {appDetails?.immediateActionTaken || incidentData?.immediateActionsTaken || incident.immediateActionsTaken || "N/A"}
                    </div>
                  </div>

                  <div className={fieldContainerClass}>
                    <div className={labelClass}>Legal Classification</div>
                    <div className={valueClass}>
                      {incident.legalClassification ? getLegalClassificationLabel(incident.legalClassification) : "N/A"}
                    </div>
                  </div>


                </div>
              </div>
            </TabsContent>

            <TabsContent value="investigation" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <Microscope size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Investigation and Analysis</h2>
                </div>

                {incident.investigationDetails ? (
                  <InvestigationAnalysisForm
                    incident={incident}
                    onSubmit={() => {}}
                    isReadOnly={true}
                    isNested={false}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Microscope className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>No investigation details available</p>
                    <p className="text-sm mt-2">The investigation needs to be completed.</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="attachments" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <Upload size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Photos/Files</h2>
                </div>

                {(() => {
                  const photos = appDetails?.uploads || incident.photos || [];
                  return photos && photos.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {photos.map((photo: any, index: number) => (
                        <Card key={index} className="overflow-hidden">
                          <div className="aspect-square bg-gray-100 flex items-center justify-center">
                            {photo.preview ? (
                              <img
                                src={photo.preview}
                                alt={`Photo ${index + 1}`}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <FileText className="h-12 w-12 text-gray-400" />
                            )}
                          </div>
                          <CardContent className="p-3">
                            <p className="text-sm font-medium truncate">
                              {photo.name || photo || `Photo ${index + 1}`}
                            </p>
                            {photo.uploadedAt && (
                              <p className="text-xs text-gray-500">
                                {format(new Date(photo.uploadedAt), "PPP")}
                              </p>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                      <p>No attachments available</p>
                    </div>
                  );
                })()}
              </div>
            </TabsContent>

            <TabsContent value="control-measures" className="space-y-6">
              <div className="form-section">
                <div className={sectionTitleClass}>
                  <div className={sectionIconClass}>
                    <FileText size={20} />
                  </div>
                  <h2 className="text-xl font-semibold">Investigation and Analysis</h2>
                </div>

                <div className="space-y-6">
                  <div className="p-4 border rounded-md bg-gray-50">
                    <p className="text-sm text-gray-500 italic mb-4">
                      The fields in the initial reporting and supplement initial reporting can be seen in the desktop, and it is locked and NOT editable. [Only Group EHS can edit it]
                    </p>

                    <div className="space-y-4">
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Incident Category, Circumstances</div>
                        <div className={valueClass}>
                          {incident.incidentCategory ? getIncidentCategoryLabel(incident.incidentCategory) : "N/A"}
                        </div>
                      </div>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Workplace Activity</div>
                        <div className={valueClass}>{incident.workplaceActivity || "N/A"}</div>
                      </div>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Incident GMS</div>
                        <div className={valueClass}>{incident.incidentGMS || "N/A"}</div>
                      </div>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Is this incident resulted in stop work order from external authority</div>
                        <div className={valueClass}>{incident.stopWorkOrder ? "Yes" : "No"}</div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className={fieldContainerClass}>
                      <div className={labelClass}>Root Cause and Analysis</div>
                      <div className={valueClass}>{incident.rootCauseAnalysis || "Not provided"}</div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="font-medium mb-2">Control Measures</h3>

                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Is control measures required?</div>
                        <div className={valueClass}>{incident.controlMeasuresRequired ? "Yes" : "No"}</div>
                      </div>

                      {incident.controlMeasuresRequired && (
                        <>
                          <div className={fieldContainerClass}>
                            <div className={labelClass}>Which control measures should be implemented in the short and long term?</div>
                            <div className={valueClass}>{incident.controlMeasuresDescription || "Not specified"}</div>
                          </div>

                          {(() => {
                            const controlMeasures = appDetails?.controlMeasures || incident.controlMeasures || [];
                            return controlMeasures && controlMeasures.length > 0 ? (
                              <div className="mt-4">
                                <h4 className="text-sm font-medium mb-2">Corrective/Control measures:</h4>
                                <div className="border rounded-md overflow-hidden">
                                  <table className="w-full text-sm">
                                    <thead className="bg-gray-100">
                                      <tr>
                                        <th className="px-4 py-2 text-left">Description</th>
                                        <th className="px-4 py-2 text-left">Due Date</th>
                                        <th className="px-4 py-2 text-left">Person Responsible</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {controlMeasures.map((measure: any, index: number) => (
                                        <tr key={index} className="border-t">
                                          <td className="px-4 py-2">{measure.controlMeasures || measure.description}</td>
                                          <td className="px-4 py-2">
                                            {(() => {
                                              const dueDate = measure.completionDate || measure.dueDate;
                                              return dueDate ? format(new Date(dueDate), "PPP") : "N/A";
                                            })()}
                                          </td>
                                          <td className="px-4 py-2">{measure.personResponsible || "N/A"}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            ) : (
                              <p className="text-gray-500 italic">No control measures have been added.</p>
                            );
                          })()}
                        </>
                      )}
                    </div>

                    <div className="border-t pt-4">
                      <div className={fieldContainerClass}>
                        <div className={labelClass}>Is risk assessments and safe working procedures need to be reviewed and updated?</div>
                        <div className={valueClass}>{incident.riskAssessmentRequired ? "Yes" : "No"}</div>
                      </div>

                      {incident.riskAssessmentRequired && (
                        <>
                          <div className={fieldContainerClass}>
                            <div className={labelClass}>Which risk assessments and safe working procedures need to be reviewed and updated?</div>
                            <div className={valueClass}>{incident.riskAssessmentDescription || "Not specified"}</div>
                          </div>

                          {(() => {
                            const riskAssessments = appDetails?.riskAssessments || incident.riskAssessments || [];
                            return riskAssessments && riskAssessments.length > 0 ? (
                              <div className="mt-4">
                                <h4 className="text-sm font-medium mb-2">Risk assessments to be reviewed:</h4>
                                <div className="border rounded-md overflow-hidden">
                                  <table className="w-full text-sm">
                                    <thead className="bg-gray-100">
                                      <tr>
                                        <th className="px-4 py-2 text-left">Name of risk assessment / safe working procedure</th>
                                        <th className="px-4 py-2 text-left">Due Date</th>
                                        <th className="px-4 py-2 text-left">Person Responsible</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {riskAssessments.map((assessment: any, index: number) => (
                                        <tr key={index} className="border-t">
                                          <td className="px-4 py-2">{assessment.name}</td>
                                          <td className="px-4 py-2">
                                            {(() => {
                                              const dueDate = assessment.completionDate || assessment.dueDate;
                                              return dueDate ? format(new Date(dueDate), "PPP") : "N/A";
                                            })()}
                                          </td>
                                          <td className="px-4 py-2">{assessment.personResponsible || "N/A"}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            ) : (
                              <p className="text-gray-500 italic">No risk assessments have been added for review.</p>
                            );
                          })()}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncidentInvestigationView;
