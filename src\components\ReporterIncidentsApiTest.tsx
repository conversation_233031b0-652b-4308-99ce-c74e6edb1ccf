import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService, getToken } from '@/lib/utils';
import { toast } from 'sonner';

const ReporterIncidentsApiTest = () => {
  const [incidents, setIncidents] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testReporterIncidentsEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setIncidents(null);

    try {
      console.log('🔄 Testing get-report-incidents-reporter endpoint...');
      console.log('🔑 Current token:', getToken() ? getToken()!.substring(0, 50) + '...' : 'NO TOKEN');
      
      const response = await ApiService.getReporterIncidents();
      console.log('✅ Reporter incidents test successful:', response);
      setIncidents(response);
      toast.success('Reporter incidents loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Reporter incidents test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load reporter incidents: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDirectFetch = async () => {
    setIsLoading(true);
    setError(null);
    setIncidents(null);

    try {
      const token = getToken();
      console.log('🔄 Testing direct fetch to get-report-incidents-reporter...');
      console.log('🔑 Using token:', token ? token.substring(0, 50) + '...' : 'NO TOKEN');

      const filters = {
        include: [
          'locationOne',
          'locationTwo',
          'locationThree',
          'locationFour',
          'locationFive',
          'locationSix',
          'incidentCircumstanceCategory',
          'incidentCircumstanceDescription',
          'incidentCircumstanceType',
          'lighting',
          'riskCategory',
          'surfaceCondition',
          'surfaceType',
          'workActivity',
          'reviewer',
          'user'
        ]
      };

      const url = `https://dev.stt-user.acuizen.com/get-report-incidents-reporter?filter=${encodeURIComponent(JSON.stringify(filters))}`;
      console.log('🔗 Direct fetch URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log('📊 Direct fetch response status:', response.status);
      console.log('📊 Direct fetch response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Direct fetch HTTP error:', response.status, errorText);
        
        if (response.status === 401) {
          console.error('🚫 AUTHORIZATION ERROR: Token may be invalid or expired');
          console.error('🔑 Token used:', token ? token.substring(0, 50) + '...' : 'NO TOKEN');
        }
        
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Direct fetch successful:', data);
      setIncidents(data);
      toast.success('Direct fetch successful!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Direct fetch failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Direct fetch failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Reporter Incidents API Test
          <div className="flex gap-2">
            <Button 
              onClick={testReporterIncidentsEndpoint} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Loading...' : 'Test ApiService'}
            </Button>
            <Button 
              onClick={testDirectFetch} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Loading...' : 'Test Direct Fetch'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/get-report-incidents-reporter?filter=...
          </code>
        </div>

        <div>
          <h3 className="font-medium mb-2">Current Token:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block break-all">
            {getToken() ? `Bearer ${getToken()!.substring(0, 100)}...` : 'NO TOKEN AVAILABLE'}
          </code>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded">
            <div className="text-red-800 font-medium">Error:</div>
            <div className="text-red-700 text-sm mt-1">{error}</div>
          </div>
        )}

        {incidents && (
          <div className="p-4 bg-green-50 border border-green-200 rounded">
            <div className="text-green-800 font-medium mb-2">Success! Response received:</div>
            <div className="text-sm text-gray-600 mb-2">
              Type: {typeof incidents} | Is Array: {Array.isArray(incidents)} | 
              Length: {Array.isArray(incidents) ? incidents.length : 'N/A'}
            </div>

            <div className="text-sm text-gray-600 mb-2">Full API Response:</div>
            <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-96">
              {JSON.stringify(incidents, null, 2)}
            </pre>
          </div>
        )}

        {!incidents && !isLoading && !error && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded">
            <div className="text-gray-600">Click "Test ApiService" or "Test Direct Fetch" to test the get-report-incidents-reporter API</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ReporterIncidentsApiTest;
