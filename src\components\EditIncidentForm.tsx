import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Incident } from "@/contexts/IncidentContext";
import IncidentReportForm from "./IncidentReportForm";

interface EditIncidentFormProps {
  incident: Incident | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (updatedIncident: Partial<Incident>) => void;
}

const EditIncidentForm: React.FC<EditIncidentFormProps> = ({
  incident,
  open,
  onOpenChange,
  onSave,
}) => {
  const [navigation, setNavigation] = useState<{
    canGoNext: boolean;
    canGoPrevious: boolean;
    currentStep: number;
    totalSteps: number;
    onNext: () => void;
    onPrevious: () => void;
    onSubmit: () => void;
    isLastStep: boolean;
  } | null>(null);

  const handleIncidentUpdated = (updatedIncident: any) => {
    console.log("🔍 EditIncidentForm: Incident updated:", updatedIncident);
    onSave(updatedIncident);
    onOpenChange(false);
  };

  // Debug log when incident changes
  React.useEffect(() => {
    if (incident) {
      console.log("🔍 EditIncidentForm: Received incident for editing:", incident);
      console.log("🔍 EditIncidentForm: Application details:", incident.applicationDetails);
    }
  }, [incident]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] flex flex-col p-0">
        <DialogClose className="absolute right-4 top-4 z-10 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>

        {/* Fixed Header */}
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b bg-white">
          <DialogTitle className="text-2xl font-bold text-gray-900">
            Edit Incident - {incident?.maskId || incident?.id}
          </DialogTitle>
          <DialogDescription className="text-gray-600 mt-2">
            Update the incident information. All fields will be pre-populated with existing data.
            {navigation && (
              <span className="block mt-1 text-sm">
                Step {navigation.currentStep} of {navigation.totalSteps}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          <IncidentReportForm
            isEditMode={true}
            existingIncident={incident}
            onIncidentCreated={handleIncidentUpdated}
            onNavigationChange={setNavigation}
          />
        </div>

        {/* Fixed Footer with Navigation */}
        {navigation && (
          <div className="flex-shrink-0 border-t bg-white p-6">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={navigation.onPrevious}
                disabled={!navigation.canGoPrevious}
              >
                Previous
              </Button>

              <div className="text-sm text-gray-500">
                Step {navigation.currentStep} of {navigation.totalSteps}
              </div>

              {navigation.isLastStep ? (
                <Button
                  onClick={navigation.onSubmit}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Update Incident
                </Button>
              ) : (
                <Button
                  onClick={navigation.onNext}
                  disabled={!navigation.canGoNext}
                >
                  Next
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditIncidentForm;
