import React, { useState, useEffect } from "react";
import { Plus, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import NewIncidentDialog from "@/components/NewIncidentDialog";
import IncidentInvestigationView from "@/components/IncidentInvestigationView";
import IncidentActionDialog from "@/components/IncidentActionDialog";
import InvestigationStatusDialog from "@/components/InvestigationStatusDialog";
import EditIncidentForm from "@/components/EditIncidentForm";
import DataTable from "@/components/DataTable";
import AllIncidentsTable from "@/components/AllIncidentsTable";
import { useUser } from "@/contexts/UserContext";
import { useIncidents, Incident } from "@/contexts/IncidentContext";
import { defaultPath } from "@/lib/paths";
import { toast } from "sonner";
import { ApiService } from "@/lib/utils";

const ReporterDashboard = () => {
  const { userName, userTitle } = useUser();
  const { incidents, addIncident, updateIncident, deleteIncident, getIncidentsByReporter, getIncidentsRequiringAction, getReporterActionIncidents } = useIncidents();
  const [isNewIncidentDialogOpen, setIsNewIncidentDialogOpen] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInvestigationStatusDialogOpen, setIsInvestigationStatusDialogOpen] = useState(false);
  const [reporterActionIncidents, setReporterActionIncidents] = useState<Incident[]>([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingIncident, setEditingIncident] = useState<Incident | null>(null);

  // Get incidents reported by the current user
  const myReportedIncidents = getIncidentsByReporter(userName);

  // Get incidents that require action from the current user (fallback)
  const myActionIncidents = getIncidentsRequiringAction(userName, 'reporter');

  // Load reporter action incidents from API
  const loadReporterActions = async () => {
    setIsLoadingActions(true);
    try {
      console.log('🔄 Loading reporter action incidents from get-report-incidents-reporter API...');
      const apiIncidents = await getReporterActionIncidents();
      setReporterActionIncidents(apiIncidents);
      toast.success(`Loaded ${apiIncidents.length} incidents from get-report-incidents-reporter API`);
    } catch (error) {
      console.error('Error loading reporter action incidents:', error);
      // Fallback to context data if API fails
      // setReporterActionIncidents(myActionIncidents);
      toast.error('Failed to load action incidents from API, using cached data');
    } finally {
      setIsLoadingActions(false);
    }
  };

  useEffect(() => {
    loadReporterActions();
  }, []); // Only run once on mount

  // All incidents for reference
  const allIncidents = incidents;
  console.log("All Incidents:", allIncidents);

  const handleView = (incident: Incident) => {
    setSelectedIncident(incident);
    setIsViewDialogOpen(true);
  };

  const handleEdit = (id: string) => {
    // First try to find the incident in reporter action incidents (from API)
    let incident = reporterActionIncidents.find(inc => inc.id === id);

    // If not found, try in the context incidents (fallback)
    if (!incident) {
      incident = incidents.find(inc => inc.id === id);
    }

    // If still not found, try in myActionIncidents (fallback)
    if (!incident) {
      incident = myActionIncidents.find(inc => inc.id === id);
    }

    if (incident) {
      setEditingIncident(incident);
      setIsEditDialogOpen(true);
    } else {
      toast.error("Incident not found");
    }
  };

  const handleTakeAction = (id: string) => {
    // First try to find the incident in reporter action incidents (from API)
    let incident = reporterActionIncidents.find(inc => inc.id === id);

    // If not found, try in the context incidents (fallback)
    if (!incident) {
      incident = incidents.find(inc => inc.id === id);
    }

    // If still not found, try in myActionIncidents (fallback)
    if (!incident) {
      incident = myActionIncidents.find(inc => inc.id === id);
    }

    if (incident) {
      setSelectedIncident(incident);
      setIsActionDialogOpen(true);
    } else {
      toast.error("Incident not found");
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this incident?")) {
      try {
        console.log(`🗑️ Deleting incident with ID: ${id}`);
        await ApiService.deleteIncident(id);

        // Also remove from local context
        deleteIncident(id);

        toast.success("Incident deleted successfully");
        console.log(`✅ Incident ${id} deleted successfully`);
      } catch (error) {
        console.error(`❌ Failed to delete incident ${id}:`, error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        toast.error(`Failed to delete incident: ${errorMessage}`);
      }
    }
  };

  const handleAttachmentChange = (id: string, attachments: File[]) => {
    // Extract URLs from the file objects
    // For files that already have URLs (from previous uploads), use those
    // For new files, create new object URLs
    const attachmentUrls = attachments.map(file => {
      // If the file name is already a URL (from our previous implementation)
      if (file.name.startsWith('blob:') || file.name.startsWith('http')) {
        return file.name;
      }
      // Otherwise, create a new URL
      return URL.createObjectURL(file);
    });

    // Update the incident with the new attachments
    updateIncident(id, { attachments: attachmentUrls });
  };

  const handleInvestigation = (incidentId: string, currentStatus: string) => {
    // Find the incident and open the investigation status dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsInvestigationStatusDialogOpen(true);
    }
  };

  const handleEditSave = (updatedIncident: Partial<Incident>) => {
    if (editingIncident) {
      // Update the incident in the context
      updateIncident(editingIncident.id, updatedIncident);

      // Update the local state if it's in the action incidents
      setReporterActionIncidents(prev =>
        prev.map(incident =>
          incident.id === editingIncident.id
            ? { ...incident, ...updatedIncident }
            : incident
        )
      );

      toast.success("Incident updated successfully");
    }
  };

  // Handle saving investigation assignment
  const handleInvestigationAssignment = (data: { leadInvestigator: string; remarks: string }) => {
    if (!selectedIncident) return;

    // Update the incident with lead investigator and remarks
    updateIncident(selectedIncident.id, {
      leadInvestigator: data.leadInvestigator,
      investigationRemarks: data.remarks,
      investigationStatus: 'in-progress' // Set status to in-progress when assigned
    });

    toast.success(`Investigation assigned for incident ${selectedIncident.id}`, {
      description: `Lead investigator assigned successfully`,
    });
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8 animate-in fade-in duration-500">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">Reporter Dashboard</h1>
          <p className="text-muted-foreground mt-1">Report and track incidents as a safety officer</p>
        </div>
        <Button
          className="bg-primary hover:bg-primary/90 shadow-md transition-all hover:shadow-lg"
          size="lg"
          onClick={() => setIsNewIncidentDialogOpen(true)}
        >
          <Plus className="mr-2" />
          New Incident
        </Button>
      </div>



      {/* New Incident Dialog */}
      <NewIncidentDialog
        open={isNewIncidentDialogOpen}
        onOpenChange={setIsNewIncidentDialogOpen}
        onIncidentCreated={(newIncident) => {
          console.log("New incident created:", newIncident);
          // Add the new incident using the context
          addIncident(newIncident as Incident);
        }}
      />

      {/* Tabs and Data Tables with Search and Filter */}
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Tabs defaultValue="my-actions" className="w-full">
          <div className="bg-muted/30 border-b">
            <TabsList className="w-full justify-start h-14 bg-transparent p-0">
              <TabsTrigger value="my-actions" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                My Actions
              </TabsTrigger>
              <TabsTrigger value="all-incidents" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                All Incidents
              </TabsTrigger>
            </TabsList>
          </div>



        <TabsContent value="my-actions" className="animate-in fade-in-50 duration-300 p-6">
          {isLoadingActions ? (
            <div className="p-8 text-center border rounded-md">
              <h3 className="text-lg font-medium mb-2">Loading your action items...</h3>
              <p className="text-muted-foreground">Fetching incidents from get-report-incidents-reporter API...</p>
            </div>
          ) : reporterActionIncidents.length === 0 && myActionIncidents.length === 0 ? (
            <div className="p-8 text-center border rounded-md">
              <h3 className="text-lg font-medium mb-2">No Action Items</h3>
              <p className="text-muted-foreground">You have no incidents that require action at this time.</p>
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                <div className="font-medium text-blue-800 mb-1">📊 Data Source: get-report-incidents-reporter API</div>
                <div className="text-blue-700">API incidents: {reporterActionIncidents.length}</div>
                <div className="text-blue-700">Fallback incidents: {myActionIncidents.length}</div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">My Action Items</h3>
               
              </div>
              <DataTable
                data={reporterActionIncidents.length > 0 ? reporterActionIncidents : myActionIncidents}
                onView={handleView}
                onTakeAction={handleTakeAction}
                onDelete={handleDelete}
                context="my-actions"
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="all-incidents" className="animate-in fade-in-50 duration-300 p-6">
          <AllIncidentsTable
            data={allIncidents}
            onAttachmentChange={handleAttachmentChange}
            handleInvestigation={handleInvestigation}
          />
        </TabsContent>
      </Tabs>
      </div>

      {/* Incident Investigation View */}
      <IncidentInvestigationView
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
        incident={selectedIncident}
      />

      {/* Incident Action Dialog */}
      <IncidentActionDialog
        open={isActionDialogOpen}
        onOpenChange={setIsActionDialogOpen}
        incident={selectedIncident}
        userRole="reporter"
        onActionComplete={(updatedIncident) => {
          console.log("Action completed in ReporterDashboard:", updatedIncident);
          // Use the updateIncident function from the context to update the incident
          updateIncident(updatedIncident.id, updatedIncident);
          // Clear the selected incident
          setSelectedIncident(null);
          // Close the dialog
          setIsActionDialogOpen(false);
        }}
      />

      {/* Investigation Status Dialog */}
      <InvestigationStatusDialog
        open={isInvestigationStatusDialogOpen}
        onOpenChange={setIsInvestigationStatusDialogOpen}
        incident={selectedIncident}
        onSave={handleInvestigationAssignment}
        onStartInvestigation={(data) => {
          // Handle starting comprehensive investigation
          console.log("Starting comprehensive investigation with data:", data);

          // Update the incident with lead investigator and start comprehensive investigation
          if (selectedIncident) {
            const updatedIncident = {
              ...selectedIncident,
              leadInvestigator: data.leadInvestigator,
              investigationRemarks: data.remarks,
              status: 'investigation' as const, // Change status to investigation
              investigationStatus: 'in-progress' as const,
              workflowStage: 'investigation' as const,
              stage: 'Investigation in Progress',
              requiresAction: false, // Remove from My Actions since it's now under investigation
              comprehensiveInvestigationStarted: true,
              comprehensiveInvestigationStartedAt: new Date(),
              comprehensiveInvestigationStartedBy: data.leadInvestigator,
            };

            // Update the incident in the context
            updateIncident(selectedIncident.id, updatedIncident);

            // Clear the selected incident
            setSelectedIncident(null);

            // Show success message
            toast.success("Comprehensive Investigation Started!", {
              description: `Lead investigator ${data.leadInvestigator} has been assigned and comprehensive investigation has started.`,
            });
          }
        }}
      />

      {/* Edit Incident Dialog */}
      <EditIncidentForm
        incident={editingIncident}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSave={handleEditSave}
      />
    </div>
  );
};

export default ReporterDashboard;
